import * as React from 'react';
import Link from 'next/link';
import { PlusIcon, SearchIcon, BuildingIcon, UserIcon } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Page, PageBody, PageHeader, PagePrimaryBar, PageTitle, PageActions } from '@/components/ui/page';
import { Routes } from '@/constants/routes';
import { getWorkspaces } from '@/data/workspaces/get-workspaces';
import { formatDistanceToNow } from 'date-fns';

export default async function WorkspacesPage() {
  const workspaces = await getWorkspaces();

  return (
    <Page>
      <PageHeader>
        <PagePrimaryBar>
          <PageTitle>Workspaces</PageTitle>
          <PageActions>
            <Button asChild>
              <Link href={Routes.CreateWorkspace} className="flex items-center gap-2">
                <PlusIcon className="size-4" />
                Create Workspace
              </Link>
            </Button>
          </PageActions>
        </PagePrimaryBar>
      </PageHeader>
      <PageBody>
        <div className="mx-auto max-w-6xl space-y-6 p-6">
          {workspaces.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <SearchIcon className="size-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No workspaces yet</h3>
              <p className="text-muted-foreground mb-6 max-w-md">
                Create your first workspace to start finding LinkedIn leads for your business.
              </p>
              <Button asChild>
                <Link href={Routes.CreateWorkspace} className="flex items-center gap-2">
                  <PlusIcon className="size-4" />
                  Create Your First Workspace
                </Link>
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {workspaces.map((workspace) => (
                <Card key={workspace.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        {workspace.targetType === 'PERSON' ? (
                          <UserIcon className="size-5 text-blue-600" />
                        ) : (
                          <BuildingIcon className="size-5 text-green-600" />
                        )}
                        <CardTitle className="text-lg">{workspace.name}</CardTitle>
                      </div>
                      <Badge variant={workspace.targetType === 'PERSON' ? 'default' : 'secondary'}>
                        {workspace.targetType === 'PERSON' ? 'People' : 'Companies'}
                      </Badge>
                    </div>
                    {workspace.description && (
                      <CardDescription className="line-clamp-2">
                        {workspace.description}
                      </CardDescription>
                    )}
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Profiles:</span>
                        <span className="font-medium">{workspace._count.profiles}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Searches:</span>
                        <span className="font-medium">{workspace._count.searches}</span>
                      </div>
                      {workspace.industry && (
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Industry:</span>
                          <span className="font-medium">{workspace.industry}</span>
                        </div>
                      )}
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Updated:</span>
                        <span className="font-medium">
                          {formatDistanceToNow(workspace.updatedAt, { addSuffix: true })}
                        </span>
                      </div>
                      <div className="pt-2">
                        <Button asChild className="w-full" variant="outline">
                          <Link href={`/dashboard/workspaces/${workspace.id}`} className="flex items-center justify-center">
                            View Workspace
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </PageBody>
    </Page>
  );
}
