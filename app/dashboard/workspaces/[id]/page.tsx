import * as React from 'react';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeftIcon, SearchIcon, UserIcon, BuildingIcon, FilterIcon, LayoutGridIcon, ListIcon } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Page, PageBody, PageHeader, PagePrimaryBar, PageTitle, PageActions } from '@/components/ui/page';
import { Routes } from '@/constants/routes';
import { getWorkspaces } from '@/data/workspaces/get-workspaces';
import { getWorkspaceProfiles } from '@/data/workspaces/get-workspace-profiles';
import { WorkspaceSearchForm } from '@/components/dashboard/workspaces/workspace-search-form';
import { WorkspaceProfileListWrapper } from '@/components/dashboard/workspaces/workspace-profile-list-wrapper';
import { WorkspaceStats } from '@/components/dashboard/workspaces/workspace-stats';
import { formatDistanceToNow } from 'date-fns';

interface WorkspacePageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function WorkspacePage({ params }: WorkspacePageProps) {
  const { id } = await params;
  const workspaces = await getWorkspaces();
  const workspace = workspaces.find(w => w.id === id);

  if (!workspace) {
    notFound();
  }

  const initialProfiles = await getWorkspaceProfiles(id);

  return (
    <Page>
      <PageHeader>
        <PagePrimaryBar>
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="sm" asChild>
              <Link href={Routes.Workspaces} className="flex items-center gap-2">
                <ArrowLeftIcon className="size-4" />
                Back
              </Link>
            </Button>
            <div className="flex items-center gap-2">
              {workspace.targetType === 'PERSON' ? (
                <UserIcon className="size-5 text-blue-600" />
              ) : (
                <BuildingIcon className="size-5 text-green-600" />
              )}
              <PageTitle>{workspace.name}</PageTitle>
            </div>
          </div>
          <PageActions>
            <Badge variant={workspace.targetType === 'PERSON' ? 'default' : 'secondary'}>
              {workspace.targetType === 'PERSON' ? 'People' : 'Companies'}
            </Badge>
          </PageActions>
        </PagePrimaryBar>
      </PageHeader>
      <PageBody>
        <div className="mx-auto max-w-7xl space-y-6 p-6">
          {/* Workspace Stats */}
          <WorkspaceStats workspace={workspace} />

          {/* Main Content Tabs */}
          <Tabs defaultValue="search" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="search" className="flex items-center gap-2">
                <SearchIcon className="size-4" />
                Search & Discover
              </TabsTrigger>
              <TabsTrigger value="profiles" className="flex items-center gap-2">
                <UserIcon className="size-4" />
                Found Profiles ({initialProfiles.length})
              </TabsTrigger>
            </TabsList>

            {/* Search Tab */}
            <TabsContent value="search" className="space-y-6">
              <WorkspaceSearchForm
                workspaceId={workspace.id}
                targetType={workspace.targetType}
                defaultCountry={workspace.country || undefined}
                defaultLanguage={workspace.language || undefined}
              />
            </TabsContent>

            {/* Profiles Tab */}
            <TabsContent value="profiles" className="space-y-6">
              <WorkspaceProfileListWrapper
                initialProfiles={initialProfiles}
                targetType={workspace.targetType}
                workspaceId={workspace.id}
              />
            </TabsContent>
          </Tabs>
        </div>
      </PageBody>
    </Page>
  );
}
