import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/db/prisma';
import { aiScoringService } from '@/lib/services/ai-scoring-service';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: workspaceId } = await params;

    // Verify workspace belongs to user's organization
    const workspace = await prisma.workspace.findFirst({
      where: {
        id: workspaceId,
        organizationId: session.user.organizationId
      }
    });

    if (!workspace) {
      return NextResponse.json({ error: 'Workspace not found' }, { status: 404 });
    }

    // Get scoring progress
    const totalProfiles = await prisma.workspaceProfile.count({
      where: { workspaceId }
    });

    const scoredProfiles = await prisma.workspaceProfile.count({
      where: { 
        workspaceId,
        aiScore: { not: null }
      }
    });

    const isProcessing = scoredProfiles < totalProfiles;

    return NextResponse.json({
      totalProfiles,
      scoredProfiles,
      isProcessing,
      progress: totalProfiles > 0 ? (scoredProfiles / totalProfiles) * 100 : 0
    });

  } catch (error) {
    console.error('AI scoring progress error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: workspaceId } = await params;
    const body = await request.json();

    // Verify workspace belongs to user's organization
    const workspace = await prisma.workspace.findFirst({
      where: {
        id: workspaceId,
        organizationId: session.user.organizationId
      }
    });

    if (!workspace) {
      return NextResponse.json({ error: 'Workspace not found' }, { status: 404 });
    }

    // Trigger AI scoring
    aiScoringService.processWorkspaceProfiles(workspace.id, {
      userQuery: body.userQuery || '',
      targetType: workspace.targetType,
      industry: workspace.industry || undefined,
      locations: body.locations || []
    }).catch(error => {
      console.error('AI scoring failed:', error);
    });

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('AI scoring trigger error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
