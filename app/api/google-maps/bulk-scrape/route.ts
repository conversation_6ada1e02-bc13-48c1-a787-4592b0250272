import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/db/prisma';
import { googleMapsService } from '@/lib/services/google-maps-service';
import { linkedInBulkScraperService } from '@/lib/services/linkedin-bulk-scraper-service';
import { revalidateTag } from 'next/cache';
import { Caching, OrganizationCacheKey } from '@/data/caching';
import { z } from 'zod';

const bulkScrapeGoogleMapsSchema = z.object({
  workspaceId: z.string().uuid(),
  query: z.string().min(1),
  location: z.string().min(1),
  limit: z.number().min(1).max(50).default(20)
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const parsedInput = bulkScrapeGoogleMapsSchema.parse(body);

    // Verify workspace belongs to user's organization
    const workspace = await prisma.workspace.findFirst({
      where: {
        id: parsedInput.workspaceId,
        organizationId: session.user.organizationId
      }
    });

    if (!workspace) {
      return NextResponse.json({ error: 'Workspace not found' }, { status: 404 });
    }

    console.log('Google Maps bulk scraping request:', {
      workspaceId: parsedInput.workspaceId,
      query: parsedInput.query,
      location: parsedInput.location,
      limit: parsedInput.limit,
      organizationId: session.user.organizationId
    });

    // Step 1: Search Google Maps businesses
    const businesses = await googleMapsService.searchByLocationAndIndustry(
      parsedInput.query,
      parsedInput.location,
      parsedInput.limit
    );

    if (businesses.length === 0) {
      return NextResponse.json({
        success: true,
        businessesFound: 0,
        businessesProcessed: 0,
        businesses: []
      });
    }

    // Step 2: Process each business
    const processedBusinesses = [];
    
    for (const business of businesses) {
      try {
        // Background enrichment (email scraping + contact creation)
        enrichGoogleMapsBusinessInBackground(
          business, 
          session.user.organizationId, 
          parsedInput.workspaceId
        ).catch(error => {
          console.error('Google Maps business background enrichment failed:', error);
        });

        processedBusinesses.push({
          business_id: business.business_id,
          name: business.name,
          phone: business.phone_number,
          website: business.website,
          address: business.full_address,
          rating: business.rating,
          review_count: business.review_count
        });

      } catch (error) {
        console.error('Failed to process Google Maps business:', business.name, error);
      }
    }

    // Invalidate contacts cache after bulk operation
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.Contacts,
        session.user.organizationId
      )
    );
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.ContactTags,
        session.user.organizationId
      )
    );

    return NextResponse.json({
      success: true,
      businessesFound: businesses.length,
      businessesProcessed: processedBusinesses.length,
      businesses: processedBusinesses
    });

  } catch (error) {
    console.error('Google Maps bulk scraping error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Background enrichment for Google Maps businesses
 */
async function enrichGoogleMapsBusinessInBackground(
  businessData: any, 
  organizationId: string, 
  workspaceId: string
) {
  try {
    console.log('Starting Google Maps business enrichment for:', businessData.name);

    let emailData = null;

    // Step 1: Scrape emails if business has valid website
    if (businessData.website && googleMapsService.isValidBusinessWebsite(businessData.website)) {
      const domain = googleMapsService.extractDomain(businessData.website);
      if (domain) {
        try {
          console.log('Scraping emails for Google Maps business domain:', domain);
          const emailResult = await linkedInBulkScraperService.scrapeEmails(domain);
          
          if (emailResult.status === 'OK' && emailResult.data?.[0]?.emails) {
            // Filter emails to only include those from the target domain
            const filteredEmails = filterEmailsByDomain(emailResult.data[0].emails, domain);
            const filteredPhones = emailResult.data[0].phone_numbers || [];
            
            if (filteredEmails.length > 0) {
              emailData = {
                ...emailResult.data[0],
                emails: filteredEmails,
                phone_numbers: filteredPhones
              };
              console.log(`Filtered emails: ${filteredEmails.length} from ${emailResult.data[0].emails.length} total`);
            }
          } else if (emailResult.status === 'RATE_LIMITED') {
            console.log('Email scraping rate limited, skipping for:', domain);
          }
        } catch (error) {
          console.error('Email scraping failed for Google Maps business:', error);
        }
      }
    } else {
      console.log('Skipping email scraping - invalid business website:', businessData.website);
    }

    // Step 2: Create Contact from Google Maps business data
    await createContactFromGoogleMapsBusiness(businessData, emailData, organizationId, workspaceId);

    console.log('Google Maps business enrichment completed for:', businessData.name);

  } catch (error) {
    console.error('Google Maps business enrichment error:', error);
  }
}

/**
 * Filter emails by domain
 */
function filterEmailsByDomain(emails: any[], targetDomain: string): any[] {
  if (!emails || !targetDomain) return [];
  
  return emails.filter(email => {
    if (!email.sources || !Array.isArray(email.sources)) return false;
    
    // Skip fake emails
    const emailValue = email.value?.toLowerCase() || '';
    const fakeEmailPatterns = [
      '<EMAIL>', '<EMAIL>', '<EMAIL>',
      '<EMAIL>', '<EMAIL>', '<EMAIL>'
    ];
    
    if (fakeEmailPatterns.some(pattern => emailValue.includes(pattern))) {
      return false;
    }
    
    // Check if from target domain
    return email.sources.some((source: string) => {
      try {
        const sourceUrl = new URL(source);
        const sourceDomain = sourceUrl.hostname.replace('www.', '').toLowerCase();
        const normalizedTargetDomain = targetDomain.replace('www.', '').toLowerCase();
        
        return sourceDomain === normalizedTargetDomain;
      } catch {
        return false;
      }
    });
  });
}

/**
 * Create Contact from Google Maps business data
 */
async function createContactFromGoogleMapsBusiness(
  businessData: any,
  emailData: any,
  organizationId: string,
  workspaceId: string
) {
  try {
    // Check if contact already exists
    const existingContact = await prisma.contact.findFirst({
      where: {
        organizationId,
        name: businessData.name
      }
    });

    if (existingContact) {
      console.log('Google Maps business contact already exists:', businessData.name);
      return existingContact;
    }

    // Generate smart tags for Google Maps business
    const smartTags = await createOrConnectGoogleMapsTags(businessData);

    // Create new business contact
    const contact = await prisma.contact.create({
      data: {
        organizationId,
        record: 'COMPANY',
        name: businessData.name,
        email: emailData?.emails?.[0]?.value || null,
        phone: businessData.phone_number || emailData?.phone_numbers?.[0]?.value || null,
        address: businessData.full_address,
        image: businessData.photos?.[0] || null,
        stage: 'LEAD',
        source: 'google_maps',
        
        // Create activity for Google Maps scrape
        activities: {
          create: {
            actionType: 'CREATE',
            actorType: 'SYSTEM',
            actorId: organizationId,
            metadata: {
              source: 'google_maps_bulk_scrape',
              businessData,
              emailData,
              workspaceId,
              scrapedAt: new Date()
            }
          }
        },

        // Add smart tags
        tags: {
          connect: smartTags
        }
      }
    });

    // Add additional emails if found
    if (emailData?.emails?.length > 1) {
      const emailPromises = emailData.emails.slice(1, 5).map((email: any, index: number) => 
        prisma.contactEmail.create({
          data: {
            contactId: contact.id,
            email: email.value,
            type: 'work',
            isPrimary: false,
            source: 'website_scrape',
            sources: email.sources
          }
        }).catch(error => {
          console.error('Failed to create additional email:', email.value, error);
        })
      );

      await Promise.all(emailPromises);
    }

    // Add additional phones if found
    if (emailData?.phone_numbers?.length > 0) {
      const phonePromises = emailData.phone_numbers.map((phone: any, index: number) => 
        prisma.contactPhone.create({
          data: {
            contactId: contact.id,
            phone: phone.value,
            type: 'work',
            isPrimary: index === 0 && !businessData.phone_number,
            source: 'website_scrape',
            sources: phone.sources
          }
        }).catch(error => {
          console.error('Failed to create phone:', phone.value, error);
        })
      );

      await Promise.all(phonePromises);
    }

    console.log('Google Maps business contact created successfully:', {
      id: contact.id,
      name: contact.name,
      phone: contact.phone,
      email: contact.email,
      rating: businessData.rating,
      reviewCount: businessData.review_count
    });

    return contact;

  } catch (error) {
    console.error('Failed to create Google Maps business contact:', error);
    throw error;
  }
}

/**
 * Create smart tags for Google Maps business
 */
async function createOrConnectGoogleMapsTags(businessData: any) {
  const tags = [];
  
  // Add business type tags
  if (businessData.types && businessData.types.length > 0) {
    const primaryType = businessData.types[0].replace(/[^a-zA-Z\s]/g, '').trim();
    if (primaryType.length <= 20) {
      tags.push(primaryType);
    }
  }
  
  // Add rating-based tag
  if (businessData.rating) {
    if (businessData.rating >= 4.5) {
      tags.push('High Rated');
    } else if (businessData.rating >= 4.0) {
      tags.push('Well Rated');
    }
  }
  
  // Add Google Maps source tag
  tags.push('Google Maps');
  
  // Limit to 3 tags
  const finalTags = tags.slice(0, 3);
  
  const connectedTags = [];
  for (const tagText of finalTags) {
    try {
      let existingTag = await prisma.contactTag.findUnique({
        where: { text: tagText }
      });
      
      if (!existingTag) {
        existingTag = await prisma.contactTag.create({
          data: { text: tagText }
        });
      }
      
      connectedTags.push({ id: existingTag.id });
    } catch (error) {
      console.error('Failed to create/connect Google Maps tag:', tagText, error);
    }
  }
  
  return connectedTags;
}
