---
title: Dependencies
description: Learn how to install dependencies for your project with various package managers.
---

Package managers simplify dependency management in software projects. They automate the process of installing, updating, and maintaining libraries and tools required for your project.

## NPM

To install dependencies using **NPM**, run the following command in your project directory:

```bash
npm install
```

This command will install all the dependencies specified in the `package.json` file of your project.

## PNPM

To install dependencies using **PNPM**, first, ensure PNPM is installed globally by running:

```bash
npm install -g pnpm
```

Once installed, navigate to your project directory and execute:

```bash
pnpm install
```

This will install all the dependencies defined in the `package.json` file efficiently, leveraging PNPM's unique caching system.

## Conclusion

Both methods ensure your project is set up with all required dependencies as defined in the `package.json` file. Choose the package manager that best suits your workflow!
