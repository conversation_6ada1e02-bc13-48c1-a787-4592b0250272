---
description: Enforces UI and styling conventions using Shadcn UI, Radix UI and Tailwind CSS for all components.
globs: **/*.{js,jsx,ts,tsx}
---
- Use Shadcn UI, Radix UI and Tailwind for components and styling.
- Make use of the components, hooks and utils.
- Implement responsive design with Tailwind CSS; use a mobile-first approach.
- Configure the Tailwind CSS theme in the `tailwind/index.cjs` file.
