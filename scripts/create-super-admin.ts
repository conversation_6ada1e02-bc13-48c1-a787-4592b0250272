import { PrismaClient, Role } from '@prisma/client';

const prisma = new PrismaClient();

async function createSuperAdmin() {
  try {
    // Get the email from command line arguments
    const email = process.argv[2];

    if (!email) {
      console.error('Please provide an email address');
      console.log('Usage: npx tsx scripts/create-super-admin.ts <email>');
      process.exit(1);
    }

    // Find the user by email
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        name: true,
        email: true,
        role: true
      }
    });

    if (!user) {
      console.error(`User with email ${email} not found`);
      process.exit(1);
    }

    if ((user.role as string) === 'SUPER_ADMIN') {
      console.log(`User ${user.name} (${user.email}) is already a super admin`);
      process.exit(0);
    }

    // Update user role to SUPER_ADMIN
    await prisma.user.update({
      where: { id: user.id },
      data: { role: 'SUPER_ADMIN' as Role }
    });

    console.log(
      `✅ Successfully promoted ${user.name} (${user.email}) to Super Admin`
    );
    console.log(
      `🔗 They can now access the super admin panel at: /super-admin`
    );
  } catch (error) {
    console.error('Error creating super admin:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

createSuperAdmin();
