import { cache } from 'react';
import { unstable_cache } from 'next/cache';

import { Caching, OrganizationCacheKey } from '@/data/caching';
import { dedupedAuth } from '@/lib/auth';
import { checkSession } from '@/lib/auth/session';
import { prisma } from '@/lib/db/prisma';

export const getWorkspaces = cache(async () => {
  const session = await dedupedAuth();
  if (!checkSession(session)) {
    return [];
  }

  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { organizationId: true }
  });

  if (!user?.organizationId) {
    return [];
  }

  // No cache - always get fresh data for real-time updates
  return await prisma.workspace.findMany({
    where: {
      organizationId: user.organizationId
    },
    select: {
      id: true,
      name: true,
      description: true,
      targetType: true,
      industry: true,
      country: true,
      language: true,
      createdAt: true,
      updatedAt: true,
      _count: {
        select: {
          profiles: true,
          searches: true
        }
      }
    },
    orderBy: {
      updatedAt: 'desc'
    }
  });
});
