import { unstable_cache as cache } from 'next/cache';

import { <PERSON>aching, SuperAdminCacheKey } from '@/data/caching';
import { dedupedAuth } from '@/lib/auth';
import { requireSuperAdmin } from '@/lib/auth/permissions';
import { prisma } from '@/lib/db/prisma';
import type { SystemMetricsDto } from '@/types/dtos/system-metrics-dto';

export async function getSystemMetrics(): Promise<SystemMetricsDto> {
  const session = await dedupedAuth();
  if (!session?.user?.id) {
    throw new Error('Unauthorized');
  }
  await requireSuperAdmin(session.user.id);

  const cachedResult = await cache(
    async (): Promise<SystemMetricsDto> => {
      const [
        totalOrganizations,
        totalUsers,
        activeOrganizations,
        totalContacts,
        organizationsCreatedThisMonth,
        usersCreatedThisMonth
      ] = await Promise.all([
        // Total organizations
        prisma.organization.count(),

        // Total users
        prisma.user.count(),

        // Active organizations (completed onboarding)
        prisma.organization.count({
          where: { completedOnboarding: true }
        }),

        // Total contacts
        prisma.contact.count(),

        // Organizations created this month
        prisma.organization.count({
          where: {
            createdAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          }
        }),

        // Users created this month
        prisma.user.count({
          where: {
            createdAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          }
        })
      ]);

      // Calculate growth rates (simplified)
      const organizationGrowthRate =
        organizationsCreatedThisMonth > 0
          ? (organizationsCreatedThisMonth /
              Math.max(totalOrganizations - organizationsCreatedThisMonth, 1)) *
            100
          : 0;

      const userGrowthRate =
        usersCreatedThisMonth > 0
          ? (usersCreatedThisMonth /
              Math.max(totalUsers - usersCreatedThisMonth, 1)) *
            100
          : 0;

      return {
        totalOrganizations,
        totalUsers,
        activeOrganizations,
        totalContacts,
        organizationGrowthRate: Number(organizationGrowthRate.toFixed(1)),
        userGrowthRate: Number(userGrowthRate.toFixed(1)),
        organizationsCreatedThisMonth,
        usersCreatedThisMonth
      };
    },
    Caching.createSuperAdminKeyParts(SuperAdminCacheKey.SystemMetrics),
    {
      tags: [Caching.createSuperAdminTag(SuperAdminCacheKey.SystemMetrics)],
      revalidate: 300 // 5 minutes
    }
  )();

  return cachedResult;
}
