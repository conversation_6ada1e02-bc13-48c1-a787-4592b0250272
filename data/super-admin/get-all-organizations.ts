import { unstable_cache as cache } from 'next/cache';

import { Caching, SuperAdminCache<PERSON>ey } from '@/data/caching';
import { dedupedAuth } from '@/lib/auth';
import { requireSuperAdmin } from '@/lib/auth/permissions';
import { prisma } from '@/lib/db/prisma';
import type { OrganizationSummaryDto } from '@/types/dtos/organization-summary-dto';

export async function getAllOrganizations(): Promise<OrganizationSummaryDto[]> {
  const session = await dedupedAuth();
  if (!session?.user?.id) {
    throw new Error('Unauthorized');
  }
  await requireSuperAdmin(session.user.id);

  const cachedResult = await cache(
    async (): Promise<OrganizationSummaryDto[]> => {
      const organizations = await prisma.organization.findMany({
        select: {
          id: true,
          name: true,
          email: true,
          tier: true,
          completedOnboarding: true,
          createdAt: true,
          stripeCustomerId: true,
          _count: {
            select: {
              users: true,
              contacts: true
            }
          },
          users: {
            where: { role: 'ADMIN' },
            select: {
              id: true,
              name: true,
              email: true,
              lastLogin: true
            },
            take: 1
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      return organizations.map((org) => ({
        id: org.id,
        name: org.name,
        email: org.email ?? undefined,
        tier: org.tier,
        status: org.completedOnboarding ? 'active' : 'pending',
        userCount: org._count.users,
        contactCount: org._count.contacts,
        createdAt: org.createdAt,
        stripeCustomerId: org.stripeCustomerId || '',
        primaryAdmin: org.users[0]
          ? {
              id: org.users[0].id,
              name: org.users[0].name,
              email: org.users[0].email!,
              lastLogin: org.users[0].lastLogin ?? undefined
            }
          : undefined
      }));
    },
    Caching.createSuperAdminKeyParts(SuperAdminCacheKey.Organizations),
    {
      tags: [Caching.createSuperAdminTag(SuperAdminCacheKey.Organizations)],
      revalidate: 300 // 5 minutes
    }
  )();

  return cachedResult;
}
