import { unstable_cache as cache } from 'next/cache';

import { Caching, SuperAdminCacheKey } from '@/data/caching';
import { dedupedAuth } from '@/lib/auth';
import { requireSuperAdmin } from '@/lib/auth/permissions';
import { prisma } from '@/lib/db/prisma';
import type { UserSummaryDto } from '@/types/dtos/user-summary-dto';

export async function getAllUsers(): Promise<UserSummaryDto[]> {
  const session = await dedupedAuth();
  if (!session?.user?.id) {
    throw new Error('Unauthorized');
  }
  await requireSuperAdmin(session.user.id);

  const cachedResult = await cache(
    async (): Promise<UserSummaryDto[]> => {
      const users = await prisma.user.findMany({
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          image: true,
          lastLogin: true,
          completedOnboarding: true,
          createdAt: true,
          organization: {
            select: {
              id: true,
              name: true,
              tier: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      return users.map((user) => ({
        id: user.id,
        name: user.name,
        email: user.email!,
        role: user.role,
        image: user.image ?? undefined,
        lastLogin: user.lastLogin ?? undefined,
        completedOnboarding: user.completedOnboarding,
        createdAt: user.createdAt,
        organization: user.organization
          ? {
              id: user.organization.id,
              name: user.organization.name,
              tier: user.organization.tier
            }
          : undefined
      }));
    },
    Caching.createSuperAdminKeyParts(SuperAdminCacheKey.Users),
    {
      tags: [Caching.createSuperAdminTag(SuperAdminCacheKey.Users)],
      revalidate: 300 // 5 minutes
    }
  )();

  return cachedResult;
}
