import { AppInfo } from '@/constants/app-info';

export enum UserCacheKey {
  OnboardingData,
  Favorites,
  ContactIsInFavorites,
  Profile,
  PersonalDetails,
  Preferences,
  MultiFactorAuthentication,
  Sessions,
  TransactionalEmails,
  MarketingEmails
}

export enum OrganizationCacheKey {
  LeadGenerationData,
  Contacts,
  ContactTags,
  Contact,
  ContactPageVisits,
  ContactTimelineEvents,
  ContactNotes,
  ContactTasks,
  OrganizationDetails,
  BusinessHours,
  SocialMedia,
  Members,
  Invitations,
  ApiKeys,
  Webhooks,
  Workspaces,
  WorkspaceProfiles,
  WorkspaceSearches
}

export enum SuperAdminCacheKey {
  SystemMetrics,
  Organizations,
  Users,
  BillingMetrics,
  SystemSettings
}

export class Caching {
  private static readonly USER_PREFIX = 'user';
  private static readonly ORGANIZATION_PREFIX = 'organization';
  private static readonly SUPER_ADMIN_PREFIX = 'super-admin';

  private static joinKeyParts(...parts: string[]): string[] {
    return parts.filter((part) => part.length > 0);
  }

  private static joinTagParts(...parts: string[]): string {
    return parts.filter((part) => part.length > 0).join(':');
  }

  public static createUserKeyParts(
    key: User<PERSON>acheKey,
    userId: string,
    ...additionalKeyParts: string[]
  ): string[] {
    if (!userId) {
      throw new Error('User ID cannot be empty');
    }
    return this.joinKeyParts(
      this.USER_PREFIX,
      userId,
      UserCacheKey[key].toLowerCase(),
      ...additionalKeyParts
    );
  }

  public static createUserTag(
    key: UserCacheKey,
    userId: string,
    ...additionalTagParts: string[]
  ): string {
    if (!userId) {
      throw new Error('User ID cannot be empty');
    }
    return this.joinTagParts(
      this.USER_PREFIX,
      userId,
      UserCacheKey[key].toLowerCase(),
      ...additionalTagParts
    );
  }

  public static createOrganizationKeyParts(
    key: OrganizationCacheKey,
    organizationId: string,
    ...additionalKeyParts: string[]
  ): string[] {
    if (!organizationId) {
      throw new Error('Organization ID cannot be empty');
    }
    return this.joinKeyParts(
      this.ORGANIZATION_PREFIX,
      organizationId,
      OrganizationCacheKey[key].toLowerCase(),
      ...additionalKeyParts
    );
  }

  public static createOrganizationTag(
    key: OrganizationCacheKey,
    organizationId: string,
    ...additionalTagParts: string[]
  ): string {
    if (!organizationId) {
      throw new Error('Organization ID cannot be empty');
    }
    return this.joinTagParts(
      this.ORGANIZATION_PREFIX,
      organizationId,
      OrganizationCacheKey[key].toLowerCase(),
      ...additionalTagParts
    );
  }

  public static createSuperAdminKeyParts(
    key: SuperAdminCacheKey,
    ...additionalKeyParts: string[]
  ): string[] {
    return this.joinKeyParts(
      this.SUPER_ADMIN_PREFIX,
      SuperAdminCacheKey[key].toLowerCase(),
      ...additionalKeyParts
    );
  }

  public static createSuperAdminTag(
    key: SuperAdminCacheKey,
    ...additionalTagParts: string[]
  ): string {
    return this.joinTagParts(
      this.SUPER_ADMIN_PREFIX,
      SuperAdminCacheKey[key].toLowerCase(),
      ...additionalTagParts
    );
  }
}

export const defaultRevalidateTimeInSeconds = AppInfo.PRODUCTION ? 3600 : 120;
