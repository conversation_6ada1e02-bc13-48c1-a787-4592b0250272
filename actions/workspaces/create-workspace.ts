'use server';

import { revalidateTag } from 'next/cache';

import { authActionClient } from '@/actions/safe-action';
import { Caching, OrganizationCacheKey } from '@/data/caching';
import { prisma } from '@/lib/db/prisma';
import { createWorkspaceSchema } from '@/schemas/workspaces/create-workspace-schema';

export const createWorkspace = authActionClient
  .metadata({ actionName: 'createWorkspace' })
  .schema(createWorkspaceSchema)
  .action(async ({ parsedInput, ctx: { session } }) => {
    // Get user's organization
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true }
    });

    if (!user?.organizationId) {
      throw new Error('User must belong to an organization');
    }

    // Create workspace
    const workspace = await prisma.workspace.create({
      data: {
        organizationId: user.organizationId,
        name: parsedInput.name,
        description: parsedInput.description,
        targetType: parsedInput.targetType,
        industry: parsedInput.industry,
        country: parsedInput.country,
        language: parsedInput.language
      },
      select: {
        id: true,
        name: true,
        description: true,
        targetType: true,
        industry: true,
        country: true,
        language: true,
        createdAt: true
      }
    });

    // Revalidate cache
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.Workspaces,
        user.organizationId
      )
    );

    return workspace;
  });
