'use server';

import { revalidateTag } from 'next/cache';

import { authActionClient } from '@/actions/safe-action';
import { Caching, OrganizationCacheKey } from '@/data/caching';
import { prisma } from '@/lib/db/prisma';
import { searchWorkspaceSchema } from '@/schemas/workspaces/search-workspace-schema';
import { aiDorkService } from '@/lib/services/ai-dork-service';
import { googleSearchService } from '@/lib/services/google-search-service';
import { aiScoringService } from '@/lib/services/ai-scoring-service';

export const searchLinkedInProfiles = authActionClient
  .metadata({ actionName: 'searchLinkedInProfiles' })
  .schema(searchWorkspaceSchema)
  .action(async ({ parsedInput, ctx: { session } }) => {
    // Get workspace and verify ownership
    const workspace = await prisma.workspace.findFirst({
      where: {
        id: parsedInput.workspaceId,
        organization: {
          users: {
            some: { id: session.user.id }
          }
        }
      },
      select: {
        id: true,
        targetType: true,
        organizationId: true,
        country: true,
        language: true,
        industry: true
      }
    });

    if (!workspace) {
      throw new Error('Workspace not found or access denied');
    }

    // Get previous searches for this workspace
    const previousSearches = await prisma.workspaceSearch.findMany({
      where: {
        workspaceId: workspace.id,
        status: 'COMPLETED'
      },
      select: {
        query: true,
        searchDork: true,
        resultsCount: true,
        executedAt: true
      },
      orderBy: {
        executedAt: 'desc'
      },
      take: 10 // Last 10 searches
    });

    // Generate search dorks using AI with previous search context
    const generatedDorks = await aiDorkService.generateDorks({
      userQuery: parsedInput.query,
      targetType: workspace.targetType,
      locations: parsedInput.locations,
      industry: workspace.industry || undefined,
      country: workspace.country || undefined,
      language: workspace.language || undefined,
      previousSearches: previousSearches
    });

    const allResults: Array<{
      url: string;
      title: string;
      description: string;
      foundInSearch: string;
      position: number;
      type: 'profile' | 'company';
    }> = [];

    // Collect all raw results first (parallel searches)
    const allRawResults: Array<{
      url: string;
      title: string;
      description: string;
      foundInSearch: string;
      position: number;
      type: 'profile' | 'company';
    }> = [];

    // Execute searches with rate limiting (sequential with delays)
    const searchPromises = generatedDorks.map(async (generatedDork, index) => {
      // Add delay between requests to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, index * 2000)); // 2 second delay between each
      try {
        // Create search record
        const searchRecord = await prisma.workspaceSearch.create({
          data: {
            workspaceId: workspace.id,
            query: parsedInput.query,
            searchDork: generatedDork.dork,
            status: 'RUNNING'
          }
        });

        // Execute Google search with fixed 50 results
        const searchResult = await googleSearchService.searchLinkedInProfiles(
          generatedDork.dork,
          {
            maxResults: 50, // Fixed at 50 results per search
            country: workspace.country || undefined,
            language: workspace.language || undefined
          }
        );

        // Extract LinkedIn URLs
        const linkedinResults = googleSearchService.extractLinkedInUrls(searchResult);

        // Update search status
        await prisma.workspaceSearch.update({
          where: { id: searchRecord.id },
          data: {
            status: 'COMPLETED',
            resultsCount: linkedinResults.length,
            executedAt: new Date()
          }
        });

        // Return raw results
        return linkedinResults.map(result => ({
          url: googleSearchService.normalizeLinkedInUrl(result.url),
          title: result.title,
          description: result.description,
          foundInSearch: generatedDork.dork,
          position: result.position,
          type: result.type
        }));

      } catch (error) {
        console.error('Search failed for dork:', generatedDork.dork, error);
        return [];
      }
    });

    // Wait for all searches to complete
    const searchResults = await Promise.all(searchPromises);

    // Flatten results
    for (const results of searchResults) {
      allRawResults.push(...results);
    }

    // Remove duplicates based on URL
    const uniqueRawResults = allRawResults.reduce((acc, current) => {
      const existing = acc.find(item => item.url === current.url);
      if (!existing) {
        acc.push(current);
      }
      return acc;
    }, [] as typeof allRawResults);

    // Use raw results directly (no AI processing during search)
    allResults.push(...uniqueRawResults);

    // Remove duplicates based on URL
    const uniqueResults = allResults.reduce((acc, current) => {
      const existing = acc.find(item => item.url === current.url);
      if (!existing) {
        acc.push(current);
      }
      return acc;
    }, [] as typeof allResults);

    // Add profiles to workspace (with deduplication)
    const addedProfiles = [];

    for (const result of uniqueResults) {
      // Validate LinkedIn URL
      if (!googleSearchService.isValidLinkedInUrl(result.url)) {
        continue;
      }

      // Check if profile already exists
      let profile = await prisma.linkedInProfile.findUnique({
        where: { linkedinUrl: result.url }
      });

      // Create profile if it doesn't exist
      if (!profile) {
        profile = await prisma.linkedInProfile.create({
          data: {
            linkedinUrl: result.url,
            profileData: null // Will be populated when user unlocks
          }
        });
      }

      // Check if already in workspace
      const existingConnection = await prisma.workspaceProfile.findUnique({
        where: {
          workspaceId_profileId: {
            workspaceId: workspace.id,
            profileId: profile.id
          }
        }
      });

      // Add to workspace if not already there
      if (!existingConnection) {
        await prisma.workspaceProfile.create({
          data: {
            workspaceId: workspace.id,
            profileId: profile.id,
            foundInSearch: result.foundInSearch,
            title: result.title,
            description: result.description
          }
        });

        addedProfiles.push({
          id: profile.id,
          url: result.url,
          title: result.title,
          description: result.description,
          type: result.type,
          position: result.position
        });
      }
    }

    // No cache invalidation needed since we removed caching

    // Trigger AI scoring in background (don't wait for it)
    if (addedProfiles.length > 0) {
      aiScoringService.processWorkspaceProfiles(workspace.id, {
        userQuery: parsedInput.query,
        targetType: workspace.targetType,
        industry: workspace.industry || undefined,
        locations: parsedInput.locations
      }).catch(error => {
        console.error('AI scoring failed:', error);
      });
    }

    return {
      totalFound: allResults.length,
      uniqueFound: uniqueResults.length,
      newProfiles: addedProfiles.length,
      alreadyInWorkspace: uniqueResults.length - addedProfiles.length,
      profiles: addedProfiles,
      searchDorks: generatedDorks.map(d => d.dork)
    };
  });
