'use server';

import { authActionClient } from '@/actions/safe-action';
import { isPaddleEnabled } from '@/lib/billing/billing-provider';
import { prisma } from '@/lib/db/prisma';
import { NotFoundError, PreConditionError } from '@/lib/validation/exceptions';

export const createPaddleCheckout = authActionClient
  .metadata({ actionName: 'createPaddleCheckout' })
  .action(async ({ ctx: { session } }) => {
    if (!isPaddleEnabled()) {
      throw new PreConditionError('Paddle billing is not enabled');
    }

    const organization = await prisma.organization.findFirst({
      where: { id: session.user.organizationId },
      select: { 
        id: true,
        paddleCustomerId: true,
        _count: { select: { users: true } }
      }
    });

    if (!organization) {
      throw new NotFoundError('Organization not found');
    }

    const proProductId = process.env.PADDLE_PRO_PRODUCT_ID;
    if (!proProductId) {
      throw new NotFoundError('No PADDLE_PRO_PRODUCT_ID found');
    }

    // For Paddle Classic, we return the product ID and user email
    // The actual checkout will be handled on the client side
    return {
      productId: parseInt(proProductId, 10),
      email: session.user.email,
      quantity: 1, // Paddle Classic doesn't support per-seat billing like Stripe
      organizationId: organization.id
    };
  });
