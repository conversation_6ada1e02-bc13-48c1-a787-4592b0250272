'use server';

import { authActionClient } from '@/actions/safe-action';
import { getBilling<PERSON><PERSON><PERSON>, BillingProvider, isPaddleEnabled } from '@/lib/billing/billing-provider';
import { prisma } from '@/lib/db/prisma';
import { stripeServer } from '@/lib/billing/stripe-server';
import { getBaseUrl } from '@/lib/urls/get-base-url';
import { Routes } from '@/constants/routes';
import { NotFoundError, PreConditionError, GatewayError } from '@/lib/validation/exceptions';
import Stripe from 'stripe';

export const createUnifiedCheckout = authActionClient
  .metadata({ actionName: 'createUnifiedCheckout' })
  .action(async ({ ctx: { session } }) => {
    const provider = getBillingProvider();

    const organization = await prisma.organization.findFirst({
      where: { id: session.user.organizationId },
      select: {
        id: true,
        stripeCustomerId: true,
        paddleCustomerId: true,
        _count: { select: { users: true } }
      }
    });

    if (!organization) {
      throw new NotFoundError('Organization not found');
    }

    switch (provider) {
      case BillingProvider.Stripe: {
        if (!organization.stripeCustomerId) {
          throw new PreConditionError('Stripe customer ID is missing');
        }

        const proProductPriceId = process.env.PRO_PRODUCT_PRICE_ID;
        if (!proProductPriceId) {
          throw new NotFoundError('No PRO_PRODUCT_PRICE_ID found');
        }

        const billingUnit = process.env.BILLING_UNIT || 'per_seat';
        const quantity = billingUnit === 'per_seat' ? organization._count.users : 1;

        try {
          const params: Stripe.Checkout.SessionCreateParams = {
            payment_method_types: ['card'],
            line_items: [
              {
                price: proProductPriceId,
                quantity
              }
            ],
            mode: 'subscription',
            customer: organization.stripeCustomerId,
            success_url: `${getBaseUrl()}${Routes.Billing}?status=success&session_id={CHECKOUT_SESSION_ID}`,
            cancel_url: `${getBaseUrl()}${Routes.Billing}?status=canceled`,
            customer_update: {
              name: 'auto',
              address: 'auto'
            }
          };
          const checkoutSession = await stripeServer.checkout.sessions.create(params);

          return {
            session: {
              id: checkoutSession.id
            }
          };
        } catch (error) {
          if (error instanceof Stripe.errors.StripeError) {
            throw new GatewayError(`Failed to create checkout session: ${error.message}`);
          }
          throw error;
        }
      }
      case BillingProvider.Paddle: {
        const proProductId = process.env.PADDLE_PRO_PRODUCT_ID;
        if (!proProductId) {
          throw new NotFoundError('No PADDLE_PRO_PRODUCT_ID found');
        }

        return {
          productId: parseInt(proProductId, 10),
          email: session.user.email,
          quantity: 1,
          organizationId: organization.id
        };
      }
      default:
        throw new Error(`Unsupported billing provider: ${provider}`);
    }
  });
