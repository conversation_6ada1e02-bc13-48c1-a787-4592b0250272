interface GoogleMapsSearchParams {
  query: string;
  limit?: number;
  country?: string;
  lang?: string;
  lat?: number;
  lng?: number;
  offset?: number;
  zoom?: number;
}

interface GoogleMapsBusiness {
  business_id: string;
  name: string;
  phone_number?: string;
  website?: string;
  full_address: string;
  latitude: number;
  longitude: number;
  types: string[];
  price_level?: string;
  rating?: number;
  review_count?: number;
  state?: string;
  working_hours?: object;
  timezone?: string;
  photos?: string[];
}

interface GoogleMapsResponse {
  status: string;
  data: GoogleMapsBusiness[];
  total_results?: number;
}

class GoogleMapsService {
  private readonly apiKey: string;
  private readonly baseUrl = 'https://maps-data.p.rapidapi.com';

  constructor() {
    this.apiKey = process.env.GOOGLE_MAP_API_KEY!;

    if (!this.apiKey) {
      throw new Error('GOOGLE_MAP_API_KEY is required');
    }
  }

  /**
   * Search businesses on Google Maps
   */
  async searchBusinesses(params: GoogleMapsSearchParams): Promise<GoogleMapsResponse> {
    console.log('Google Maps Search Request:', {
      url: `${this.baseUrl}/searchmaps.php`,
      params
    });

    try {
      const searchParams = new URLSearchParams({
        query: params.query,
        limit: (params.limit || 20).toString(),
        country: params.country || 'us',
        lang: params.lang || 'en',
        offset: (params.offset || 0).toString(),
        zoom: (params.zoom || 13).toString()
      });

      // Add lat/lng if provided
      if (params.lat && params.lng) {
        searchParams.append('lat', params.lat.toString());
        searchParams.append('lng', params.lng.toString());
      }

      const response = await fetch(`${this.baseUrl}/searchmaps.php?${searchParams.toString()}`, {
        method: 'GET',
        headers: {
          'x-rapidapi-host': 'maps-data.p.rapidapi.com',
          'x-rapidapi-key': this.apiKey
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Google Maps API Error:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        throw new Error(`Google Maps API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      console.log('Google Maps Search Response:', {
        status: data.status,
        businessesFound: data.data?.length || 0,
        totalResults: data.total_results
      });

      return data;

    } catch (error) {
      console.error('Google Maps Service Error:', error);
      throw error;
    }
  }

  /**
   * Search businesses by location and industry
   */
  async searchByLocationAndIndustry(
    industry: string,
    location: string,
    limit: number = 20
  ): Promise<GoogleMapsBusiness[]> {
    // Clean and format the query properly
    const cleanedQuery = this.formatSearchQuery(industry, location);

    try {
      // Try to get coordinates for location (optional enhancement)
      const coordinates = await this.getLocationCoordinates(location);

      // Determine country and language based on location
      const { country, lang } = this.getLocationSettings(location);

      const params: GoogleMapsSearchParams = {
        query: cleanedQuery,
        limit,
        country,
        lang,
        lat: coordinates?.lat,
        lng: coordinates?.lng
      };

      const response = await this.searchBusinesses(params);

      if (response.status === 'OK' && response.data) {
        return response.data;
      }

      return [];

    } catch (error) {
      console.error('Location and industry search failed:', error);
      return [];
    }
  }

  /**
   * Format search query properly to avoid duplication
   */
  private formatSearchQuery(industry: string, location: string): string {
    // Remove location from industry if it's already included
    const normalizedLocation = location.toLowerCase().trim();
    const normalizedIndustry = industry.toLowerCase();

    // Check if location is already mentioned in industry
    const locationVariants = [
      normalizedLocation,
      normalizedLocation + 'da',
      normalizedLocation + 'de',
      normalizedLocation + ' bulunan',
      'in ' + normalizedLocation
    ];

    const hasLocationInIndustry = locationVariants.some(variant =>
      normalizedIndustry.includes(variant)
    );

    if (hasLocationInIndustry) {
      // Location already in industry, use industry as is
      return industry.trim();
    } else {
      // Add location to industry
      return `${industry.trim()} ${location.trim()}`;
    }
  }

  /**
   * Get country and language settings based on location
   */
  private getLocationSettings(location: string): { country: string; lang: string } {
    const normalizedLocation = location.toLowerCase().trim();

    // Turkish locations
    if (['istanbul', 'ankara', 'izmir', 'bursa', 'antalya', 'adana', 'konya', 'gaziantep', 'mersin', 'diyarbakır'].includes(normalizedLocation) ||
        normalizedLocation.includes('türkiye') || normalizedLocation.includes('turkey')) {
      return { country: 'tr', lang: 'tr' };
    }

    // US locations
    if (['new york', 'los angeles', 'chicago', 'houston', 'phoenix', 'philadelphia', 'san antonio', 'san diego', 'dallas', 'san jose'].includes(normalizedLocation) ||
        normalizedLocation.includes('usa') || normalizedLocation.includes('united states')) {
      return { country: 'us', lang: 'en' };
    }

    // UK locations
    if (['london', 'birmingham', 'manchester', 'glasgow', 'liverpool', 'leeds', 'sheffield', 'edinburgh', 'bristol', 'cardiff'].includes(normalizedLocation) ||
        normalizedLocation.includes('uk') || normalizedLocation.includes('united kingdom')) {
      return { country: 'gb', lang: 'en' };
    }

    // German locations
    if (['berlin', 'hamburg', 'munich', 'cologne', 'frankfurt', 'stuttgart', 'düsseldorf', 'dortmund', 'essen', 'leipzig'].includes(normalizedLocation) ||
        normalizedLocation.includes('germany') || normalizedLocation.includes('deutschland')) {
      return { country: 'de', lang: 'de' };
    }

    // French locations
    if (['paris', 'marseille', 'lyon', 'toulouse', 'nice', 'nantes', 'strasbourg', 'montpellier', 'bordeaux', 'lille'].includes(normalizedLocation) ||
        normalizedLocation.includes('france')) {
      return { country: 'fr', lang: 'fr' };
    }

    // Default to US/English
    return { country: 'us', lang: 'en' };
  }

  /**
   * Get coordinates for a location (enhanced implementation)
   */
  private async getLocationCoordinates(location: string): Promise<{lat: number, lng: number} | null> {
    // Enhanced location to coordinates mapping
    const locationMap: Record<string, {lat: number, lng: number}> = {
      // Turkey
      'istanbul': { lat: 41.0082, lng: 28.9784 },
      'ankara': { lat: 39.9334, lng: 32.8597 },
      'izmir': { lat: 38.4192, lng: 27.1287 },
      'bursa': { lat: 40.1826, lng: 29.0665 },
      'antalya': { lat: 36.8969, lng: 30.7133 },
      'adana': { lat: 37.0000, lng: 35.3213 },
      'konya': { lat: 37.8713, lng: 32.4846 },
      'gaziantep': { lat: 37.0662, lng: 37.3833 },
      'mersin': { lat: 36.8000, lng: 34.6333 },
      'diyarbakır': { lat: 37.9144, lng: 40.2306 },

      // US
      'new york': { lat: 40.7128, lng: -74.0060 },
      'los angeles': { lat: 34.0522, lng: -118.2437 },
      'chicago': { lat: 41.8781, lng: -87.6298 },
      'houston': { lat: 29.7604, lng: -95.3698 },
      'phoenix': { lat: 33.4484, lng: -112.0740 },
      'philadelphia': { lat: 39.9526, lng: -75.1652 },
      'san antonio': { lat: 29.4241, lng: -98.4936 },
      'san diego': { lat: 32.7157, lng: -117.1611 },
      'dallas': { lat: 32.7767, lng: -96.7970 },
      'san jose': { lat: 37.3382, lng: -121.8863 },

      // UK
      'london': { lat: 51.5074, lng: -0.1278 },
      'birmingham': { lat: 52.4862, lng: -1.8904 },
      'manchester': { lat: 53.4808, lng: -2.2426 },
      'glasgow': { lat: 55.8642, lng: -4.2518 },
      'liverpool': { lat: 53.4084, lng: -2.9916 },
      'leeds': { lat: 53.8008, lng: -1.5491 },
      'sheffield': { lat: 53.3811, lng: -1.4701 },
      'edinburgh': { lat: 55.9533, lng: -3.1883 },
      'bristol': { lat: 51.4545, lng: -2.5879 },
      'cardiff': { lat: 51.4816, lng: -3.1791 },

      // Germany
      'berlin': { lat: 52.5200, lng: 13.4050 },
      'hamburg': { lat: 53.5511, lng: 9.9937 },
      'munich': { lat: 48.1351, lng: 11.5820 },
      'cologne': { lat: 50.9375, lng: 6.9603 },
      'frankfurt': { lat: 50.1109, lng: 8.6821 },
      'stuttgart': { lat: 48.7758, lng: 9.1829 },
      'düsseldorf': { lat: 51.2277, lng: 6.7735 },
      'dortmund': { lat: 51.5136, lng: 7.4653 },
      'essen': { lat: 51.4556, lng: 7.0116 },
      'leipzig': { lat: 51.3397, lng: 12.3731 },

      // France
      'paris': { lat: 48.8566, lng: 2.3522 },
      'marseille': { lat: 43.2965, lng: 5.3698 },
      'lyon': { lat: 45.7640, lng: 4.8357 },
      'toulouse': { lat: 43.6047, lng: 1.4442 },
      'nice': { lat: 43.7102, lng: 7.2620 },
      'nantes': { lat: 47.2184, lng: -1.5536 },
      'strasbourg': { lat: 48.5734, lng: 7.7521 },
      'montpellier': { lat: 43.6110, lng: 3.8767 },
      'bordeaux': { lat: 44.8378, lng: -0.5792 },
      'lille': { lat: 50.6292, lng: 3.0573 },

      // Other major cities
      'tokyo': { lat: 35.6762, lng: 139.6503 },
      'sydney': { lat: -33.8688, lng: 151.2093 },
      'toronto': { lat: 43.6532, lng: -79.3832 },
      'vancouver': { lat: 49.2827, lng: -123.1207 },
      'amsterdam': { lat: 52.3676, lng: 4.9041 },
      'rome': { lat: 41.9028, lng: 12.4964 },
      'madrid': { lat: 40.4168, lng: -3.7038 },
      'barcelona': { lat: 41.3851, lng: 2.1734 }
    };

    const normalizedLocation = location.toLowerCase().trim();
    return locationMap[normalizedLocation] || null;
  }

  /**
   * Extract domain from website URL
   */
  extractDomain(website: string): string | null {
    if (!website) return null;
    
    try {
      const url = new URL(website.startsWith('http') ? website : `https://${website}`);
      return url.hostname.replace('www.', '');
    } catch {
      return null;
    }
  }

  /**
   * Validate if website is business website (not social media)
   */
  isValidBusinessWebsite(website: string): boolean {
    if (!website) return false;
    
    try {
      const url = new URL(website.startsWith('http') ? website : `https://${website}`);
      const hostname = url.hostname.toLowerCase();
      
      // Block social media domains
      const blockedDomains = [
        'instagram.com', 'facebook.com', 'twitter.com', 'x.com',
        'linkedin.com', 'youtube.com', 'tiktok.com', 'snapchat.com',
        'pinterest.com', 'whatsapp.com', 'telegram.org'
      ];
      
      return !blockedDomains.some(blocked => 
        hostname.includes(blocked) || hostname.endsWith(`.${blocked}`)
      );
    } catch {
      return false;
    }
  }
}

// Export singleton instance
export const googleMapsService = new GoogleMapsService();

// Export types
export type { GoogleMapsSearchParams, GoogleMapsBusiness, GoogleMapsResponse };
