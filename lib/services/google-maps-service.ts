interface GoogleMapsSearchParams {
  query: string;
  limit?: number;
  country?: string;
  lang?: string;
  lat?: number;
  lng?: number;
  offset?: number;
  zoom?: number;
}

interface GoogleMapsBusiness {
  business_id: string;
  name: string;
  phone_number?: string;
  website?: string;
  full_address: string;
  latitude: number;
  longitude: number;
  types: string[];
  price_level?: string;
  rating?: number;
  review_count?: number;
  state?: string;
  working_hours?: object;
  timezone?: string;
  photos?: string[];
}

interface GoogleMapsResponse {
  status: string;
  data: GoogleMapsBusiness[];
  total_results?: number;
}

class GoogleMapsService {
  private readonly apiKey: string;
  private readonly baseUrl = 'https://maps-data.p.rapidapi.com';

  constructor() {
    this.apiKey = process.env.GOOGLE_MAP_API_KEY!;

    if (!this.apiKey) {
      throw new Error('GOOGLE_MAP_API_KEY is required');
    }
  }

  /**
   * Search businesses on Google Maps
   */
  async searchBusinesses(params: GoogleMapsSearchParams): Promise<GoogleMapsResponse> {
    console.log('Google Maps Search Request:', {
      url: `${this.baseUrl}/searchmaps.php`,
      params
    });

    try {
      const searchParams = new URLSearchParams({
        query: params.query,
        limit: (params.limit || 20).toString(),
        country: params.country || 'us',
        lang: params.lang || 'en',
        offset: (params.offset || 0).toString(),
        zoom: (params.zoom || 13).toString()
      });

      // Add lat/lng if provided
      if (params.lat && params.lng) {
        searchParams.append('lat', params.lat.toString());
        searchParams.append('lng', params.lng.toString());
      }

      const response = await fetch(`${this.baseUrl}/searchmaps.php?${searchParams.toString()}`, {
        method: 'GET',
        headers: {
          'x-rapidapi-host': 'maps-data.p.rapidapi.com',
          'x-rapidapi-key': this.apiKey
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Google Maps API Error:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        throw new Error(`Google Maps API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      console.log('Google Maps Search Response:', {
        status: data.status,
        businessesFound: data.data?.length || 0,
        totalResults: data.total_results
      });

      return data;

    } catch (error) {
      console.error('Google Maps Service Error:', error);
      throw error;
    }
  }

  /**
   * Search businesses by location and industry
   */
  async searchByLocationAndIndustry(
    industry: string, 
    location: string, 
    limit: number = 20
  ): Promise<GoogleMapsBusiness[]> {
    const query = `${industry} ${location}`;
    
    try {
      // Try to get coordinates for location (optional enhancement)
      const coordinates = await this.getLocationCoordinates(location);
      
      const params: GoogleMapsSearchParams = {
        query,
        limit,
        lat: coordinates?.lat,
        lng: coordinates?.lng
      };

      const response = await this.searchBusinesses(params);
      
      if (response.status === 'OK' && response.data) {
        return response.data;
      }

      return [];

    } catch (error) {
      console.error('Location and industry search failed:', error);
      return [];
    }
  }

  /**
   * Get coordinates for a location (basic implementation)
   */
  private async getLocationCoordinates(location: string): Promise<{lat: number, lng: number} | null> {
    // Basic location to coordinates mapping
    const locationMap: Record<string, {lat: number, lng: number}> = {
      'london': { lat: 51.5074, lng: -0.1278 },
      'new york': { lat: 40.7128, lng: -74.0060 },
      'los angeles': { lat: 34.0522, lng: -118.2437 },
      'chicago': { lat: 41.8781, lng: -87.6298 },
      'istanbul': { lat: 41.0082, lng: 28.9784 },
      'ankara': { lat: 39.9334, lng: 32.8597 },
      'izmir': { lat: 38.4192, lng: 27.1287 },
      'paris': { lat: 48.8566, lng: 2.3522 },
      'berlin': { lat: 52.5200, lng: 13.4050 },
      'tokyo': { lat: 35.6762, lng: 139.6503 },
      'sydney': { lat: -33.8688, lng: 151.2093 }
    };

    const normalizedLocation = location.toLowerCase().trim();
    return locationMap[normalizedLocation] || null;
  }

  /**
   * Extract domain from website URL
   */
  extractDomain(website: string): string | null {
    if (!website) return null;
    
    try {
      const url = new URL(website.startsWith('http') ? website : `https://${website}`);
      return url.hostname.replace('www.', '');
    } catch {
      return null;
    }
  }

  /**
   * Validate if website is business website (not social media)
   */
  isValidBusinessWebsite(website: string): boolean {
    if (!website) return false;
    
    try {
      const url = new URL(website.startsWith('http') ? website : `https://${website}`);
      const hostname = url.hostname.toLowerCase();
      
      // Block social media domains
      const blockedDomains = [
        'instagram.com', 'facebook.com', 'twitter.com', 'x.com',
        'linkedin.com', 'youtube.com', 'tiktok.com', 'snapchat.com',
        'pinterest.com', 'whatsapp.com', 'telegram.org'
      ];
      
      return !blockedDomains.some(blocked => 
        hostname.includes(blocked) || hostname.endsWith(`.${blocked}`)
      );
    } catch {
      return false;
    }
  }
}

// Export singleton instance
export const googleMapsService = new GoogleMapsService();

// Export types
export type { GoogleMapsSearchParams, GoogleMapsBusiness, GoogleMapsResponse };
