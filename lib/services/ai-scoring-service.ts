import OpenAI from 'openai';
import { prisma } from '@/lib/db/prisma';

interface ProfileToScore {
  id: string;
  title: string | null;
  description: string | null;
  linkedinUrl: string;
}

interface ScoringConfig {
  userQuery: string;
  targetType: 'PERSON' | 'COMPANY';
  industry?: string;
  locations?: string[];
}

interface ScoringResult {
  profileId: string;
  score: number;
  reason: string;
  extractedInfo: {
    name?: string;
    position?: string;
    company?: string;
    location?: string;
  };
}

class AIScoringService {
  private openai: OpenAI;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
  }

  /**
   * Score profiles in batches of 10
   */
  async scoreProfilesBatch(
    profiles: ProfileToScore[],
    config: ScoringConfig
  ): Promise<ScoringResult[]> {
    if (profiles.length === 0) return [];

    try {
      const prompt = this.buildScoringPrompt(profiles, config);
      
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are an expert at analyzing LinkedIn profiles and scoring their relevance. Be fast and accurate.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 1000
      });

      const content = response.choices[0]?.message?.content;
      if (content) {
        return this.parseScoringResults(content, profiles);
      }

      return this.fallbackScoring(profiles);
    } catch (error) {
      console.error('AI scoring error:', error);
      return this.fallbackScoring(profiles);
    }
  }

  /**
   * Build scoring prompt
   */
  private buildScoringPrompt(profiles: ProfileToScore[], config: ScoringConfig): string {
    const { userQuery, targetType, industry, locations } = config;

    const profilesText = profiles.map((profile, index) => 
      `${index + 1}. "${profile.title}" - ${profile.description || 'No description'}`
    ).join('\n');

    return `
USER WANTS: "${userQuery}"
TARGET: ${targetType}
INDUSTRY: ${industry || 'Any'}
LOCATIONS: ${locations?.join(', ') || 'Any'}

SCORE THESE ${profiles.length} PROFILES (1-10):
${profilesText}

SCORING GUIDE:
9-10: Perfect match (exactly what user wants)
7-8: Very good match (close to requirements)
5-6: Decent match (some relevance)
3-4: Poor match (little relevance)
1-2: No match (irrelevant)

EXTRACT: name, position, company, location

JSON ONLY:
[
  {
    "index": 1,
    "score": 8.5,
    "reason": "Brief reason",
    "name": "Name",
    "position": "Position",
    "company": "Company",
    "location": "Location"
  }
]

BE FAST AND ACCURATE.
`;
  }

  /**
   * Parse scoring results
   */
  private parseScoringResults(content: string, profiles: ProfileToScore[]): ScoringResult[] {
    try {
      const jsonMatch = content.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        if (Array.isArray(parsed)) {
          return parsed
            .filter(item => item.index && item.index <= profiles.length)
            .map(item => {
              const profile = profiles[item.index - 1];
              return {
                profileId: profile.id,
                score: Math.max(0, Math.min(10, item.score || 0)),
                reason: item.reason || 'No reason provided',
                extractedInfo: {
                  name: item.name,
                  position: item.position,
                  company: item.company,
                  location: item.location
                }
              };
            });
        }
      }

      return this.fallbackScoring(profiles);
    } catch (error) {
      console.error('Error parsing scoring results:', error);
      return this.fallbackScoring(profiles);
    }
  }

  /**
   * Fallback scoring when AI fails
   */
  private fallbackScoring(profiles: ProfileToScore[]): ScoringResult[] {
    return profiles.map(profile => ({
      profileId: profile.id,
      score: 5.0, // Neutral score
      reason: 'AI scoring failed - manual review needed',
      extractedInfo: {
        name: profile.title?.split(' - ')[0] || 'Unknown',
        position: '',
        company: '',
        location: ''
      }
    }));
  }

  /**
   * Update profile scores in database
   */
  async updateProfileScores(results: ScoringResult[]): Promise<void> {
    const updatePromises = results.map(result => 
      prisma.workspaceProfile.update({
        where: { id: result.profileId },
        data: {
          aiScore: result.score,
          aiReason: result.reason,
          aiExtractedData: result.extractedInfo
        }
      })
    );

    await Promise.all(updatePromises);
  }

  /**
   * Process all unscored profiles for a workspace
   */
  async processWorkspaceProfiles(workspaceId: string, config: ScoringConfig): Promise<void> {
    // Get unscored profiles
    const unscoredProfiles = await prisma.workspaceProfile.findMany({
      where: {
        workspaceId,
        aiScore: null
      },
      select: {
        id: true,
        title: true,
        description: true,
        profile: {
          select: {
            linkedinUrl: true
          }
        }
      },
      orderBy: {
        addedAt: 'desc'
      }
    });

    if (unscoredProfiles.length === 0) return;

    // Process in batches of 10
    const batchSize = 10;
    for (let i = 0; i < unscoredProfiles.length; i += batchSize) {
      const batch = unscoredProfiles.slice(i, i + batchSize);
      
      const profilesToScore: ProfileToScore[] = batch.map(p => ({
        id: p.id,
        title: p.title,
        description: p.description,
        linkedinUrl: p.profile.linkedinUrl
      }));

      // Score batch
      const results = await this.scoreProfilesBatch(profilesToScore, config);
      
      // Update database
      await this.updateProfileScores(results);

      // Small delay between batches
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}

// Export singleton instance
export const aiScoringService = new AIScoringService();
export type { ScoringResult, ScoringConfig };
