// Google Search API Service for LinkedIn Profile Discovery

export interface GoogleSearchResult {
  search_term: string;
  results: Array<{
    position: number;
    url: string;
    title: string;
    description: string;
  }>;
  next_page?: number;
  next_start?: number;
  total_results?: string;
}

export interface GoogleSearchParams {
  query: string;
  num?: number; // Number of results (max 100)
  start?: number; // Starting position for pagination
  gl?: string; // Country code (tr, us, etc.)
  hl?: string; // Language code (tr, en, etc.)
  lr?: string; // Language restriction
  cr?: string; // Country restriction
  tbs?: string; // Time-based search (qdr:y for last year)
  safe?: string; // Safe search
}

class GoogleSearchService {
  private readonly apiKey: string;
  private readonly baseUrl = 'https://google-search116.p.rapidapi.com';
  private readonly headers: Record<string, string>;

  constructor() {
    this.apiKey = process.env.GOOGLE_SEARCH_API_KEY || '';
    if (!this.apiKey) {
      throw new Error('GOOGLE_SEARCH_API_KEY is required');
    }

    this.headers = {
      'x-rapidapi-key': this.apiKey,
      'x-rapidapi-host': 'google-search116.p.rapidapi.com',
      'Content-Type': 'application/json'
    };
  }

  /**
   * Search Google with the given parameters
   */
  async search(params: GoogleSearchParams): Promise<GoogleSearchResult> {
    try {
      const searchParams = new URLSearchParams();
      
      // Add query parameter
      searchParams.append('query', params.query);
      
      // Add optional parameters
      if (params.num) searchParams.append('num', params.num.toString());
      if (params.start) searchParams.append('start', params.start.toString());
      if (params.gl) searchParams.append('gl', params.gl);
      if (params.hl) searchParams.append('hl', params.hl);
      if (params.lr) searchParams.append('lr', params.lr);
      if (params.cr) searchParams.append('cr', params.cr);
      if (params.tbs) searchParams.append('tbs', params.tbs);
      if (params.safe) searchParams.append('safe', params.safe);

      const url = `${this.baseUrl}/?${searchParams.toString()}`;

      console.log('Google Search API Request:', {
        url,
        query: params.query,
        params: Object.fromEntries(searchParams)
      });

      // Retry logic for rate limiting
      const maxRetries = 3;
      let lastError: Error | null = null;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          const response = await fetch(url, {
            method: 'GET',
            headers: this.headers
          });

          if (response.status === 429) {
            // Rate limited - wait and retry
            const waitTime = attempt * 3000; // 3s, 6s, 9s
            console.log(`Rate limited. Waiting ${waitTime}ms before retry ${attempt}/${maxRetries}`);
            if (attempt < maxRetries) {
              await new Promise(resolve => setTimeout(resolve, waitTime));
              continue;
            }
          }

          if (!response.ok) {
            const errorText = await response.text();
            console.error('Google Search API Error:', {
              status: response.status,
              statusText: response.statusText,
              error: errorText
            });
            throw new Error(`Google Search API error: ${response.status} ${response.statusText}`);
          }

          // Success - return data
          const data = await response.json();

          console.log('Google Search API Response:', {
            query: params.query,
            resultsCount: data.results?.length || 0,
            totalResults: data.total_results
          });

          return data;

        } catch (error) {
          lastError = error as Error;
          if (attempt === maxRetries) {
            throw lastError;
          }
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }

      throw lastError || new Error('Max retries exceeded');
    } catch (error) {
      console.error('Google Search Service Error:', error);
      throw error;
    }
  }

  /**
   * Search for LinkedIn profiles using optimized parameters
   */
  async searchLinkedInProfiles(
    dork: string,
    options: {
      maxResults?: number;
      country?: string;
      language?: string;
      timeFilter?: 'qdr:y' | 'qdr:m6' | 'qdr:m';
    } = {}
  ): Promise<GoogleSearchResult> {
    const params: GoogleSearchParams = {
      query: dork,
      num: Math.min(options.maxResults || 50, 100), // API limit is 100
      gl: options.country || 'tr',
      hl: options.language || 'tr',
      safe: 'active'
    };

    // Add language restriction
    if (options.language) {
      params.lr = `lang_${options.language}`;
    }

    // Add country restriction
    if (options.country) {
      params.cr = `country${options.country.toUpperCase()}`;
    }

    // Add time filter for more recent profiles
    if (options.timeFilter) {
      params.tbs = options.timeFilter;
    }

    return this.search(params);
  }

  /**
   * Extract LinkedIn URLs from search results
   */
  extractLinkedInUrls(searchResult: GoogleSearchResult): Array<{
    url: string;
    title: string;
    description: string;
    position: number;
    type: 'profile' | 'company';
  }> {
    if (!searchResult.results) {
      return [];
    }

    return searchResult.results
      .filter(result => {
        const url = result.url.toLowerCase();
        return url.includes('linkedin.com/in') || url.includes('linkedin.com/company');
      })
      .map(result => ({
        url: result.url,
        title: result.title,
        description: result.description,
        position: result.position,
        type: result.url.includes('/in/') ? 'profile' as const : 'company' as const
      }))
      .sort((a, b) => a.position - b.position);
  }

  /**
   * Validate if a URL is a valid LinkedIn profile/company URL
   */
  isValidLinkedInUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase();
      const pathname = urlObj.pathname.toLowerCase();

      // Check if it's a LinkedIn domain
      if (!hostname.includes('linkedin.com')) {
        return false;
      }

      // Check if it's a profile or company page
      return pathname.includes('/in/') || pathname.includes('/company/');
    } catch {
      return false;
    }
  }

  /**
   * Clean and normalize LinkedIn URL
   */
  normalizeLinkedInUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      
      // Remove query parameters and fragments
      urlObj.search = '';
      urlObj.hash = '';
      
      // Ensure it ends with /
      if (!urlObj.pathname.endsWith('/')) {
        urlObj.pathname += '/';
      }

      return urlObj.toString();
    } catch {
      return url;
    }
  }
}

// Export singleton instance
export const googleSearchService = new GoogleSearchService();
