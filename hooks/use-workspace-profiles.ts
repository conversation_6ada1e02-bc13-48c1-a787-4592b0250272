'use client';

import { useState, useEffect, useCallback } from 'react';

interface WorkspaceProfile {
  id: string;
  title: string | null;
  description: string | null;
  foundInSearch: string;
  addedAt: Date;
  aiScore: number | null;
  aiReason: string | null;
  aiExtractedData: any;
  aiProcessedAt: Date | null;
  profile: {
    id: string;
    linkedinUrl: string;
    profileData: any;
    scrapedAt: Date | null;
  };
}

export function useWorkspaceProfiles(workspaceId: string, initialProfiles: WorkspaceProfile[] = []) {
  const [profiles, setProfiles] = useState<WorkspaceProfile[]>(initialProfiles);
  const [isLoading, setIsLoading] = useState(false);
  const [shouldPoll, setShouldPoll] = useState(true);

  const refreshProfiles = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/workspaces/${workspaceId}/profiles`);
      if (response.ok) {
        const data = await response.json();
        setProfiles(data);

        // Check if all profiles have AI scores - if so, stop polling
        const totalProfiles = data.length;
        const scoredProfiles = data.filter((p: WorkspaceProfile) => p.aiScore !== null).length;

        if (totalProfiles > 0 && scoredProfiles === totalProfiles) {
          setShouldPoll(false); // Stop polling when all profiles are scored
        }
      }
    } catch (error) {
      console.error('Failed to refresh profiles:', error);
    } finally {
      setIsLoading(false);
    }
  }, [workspaceId]);

  // Auto-refresh only when shouldPoll is true
  useEffect(() => {
    if (!shouldPoll) return;

    const interval = setInterval(refreshProfiles, 10000); // 10 seconds
    return () => clearInterval(interval);
  }, [refreshProfiles, shouldPoll]);

  // Reset polling when profiles change (new search)
  useEffect(() => {
    const totalProfiles = profiles.length;
    const scoredProfiles = profiles.filter(p => p.aiScore !== null).length;

    if (totalProfiles > 0 && scoredProfiles < totalProfiles) {
      setShouldPoll(true); // Resume polling if there are unscored profiles
    }
  }, [profiles]);

  return {
    profiles,
    isLoading,
    refreshProfiles,
    shouldPoll
  };
}
