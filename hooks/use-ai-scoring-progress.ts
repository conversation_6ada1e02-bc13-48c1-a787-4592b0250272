'use client';

import { useState, useEffect, useCallback } from 'react';

interface ScoringProgress {
  totalProfiles: number;
  scoredProfiles: number;
  isProcessing: boolean;
  progress: number;
}

export function useAIScoringProgress(workspaceId: string) {
  const [progress, setProgress] = useState<ScoringProgress>({
    totalProfiles: 0,
    scoredProfiles: 0,
    isProcessing: false,
    progress: 0
  });
  const [isLoading, setIsLoading] = useState(false);

  const fetchProgress = useCallback(async () => {
    try {
      const response = await fetch(`/api/workspaces/${workspaceId}/ai-scoring`);
      if (response.ok) {
        const data = await response.json();
        setProgress(data);
        return data;
      }
    } catch (error) {
      console.error('Failed to fetch AI scoring progress:', error);
    }
  }, [workspaceId]);

  const triggerScoring = useCallback(async (userQuery: string, locations?: string[]) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/workspaces/${workspaceId}/ai-scoring`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userQuery,
          locations
        })
      });

      if (response.ok) {
        // Start polling for progress
        const interval = setInterval(async () => {
          const currentProgress = await fetchProgress();
          if (currentProgress && !currentProgress.isProcessing) {
            clearInterval(interval);
            setIsLoading(false);
          }
        }, 2000); // Poll every 2 seconds

        return true;
      }
    } catch (error) {
      console.error('Failed to trigger AI scoring:', error);
      setIsLoading(false);
    }
    return false;
  }, [workspaceId, fetchProgress]);

  // Initial fetch
  useEffect(() => {
    fetchProgress();
  }, [fetchProgress]);

  // Auto-refresh when processing
  useEffect(() => {
    if (progress.isProcessing) {
      const interval = setInterval(fetchProgress, 5000); // Poll every 5 seconds
      return () => clearInterval(interval);
    }
  }, [progress.isProcessing, fetchProgress]);

  return {
    progress,
    isLoading,
    fetchProgress,
    triggerScoring
  };
}
