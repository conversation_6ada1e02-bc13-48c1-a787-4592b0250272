import * as React from 'react';
import Link from 'next/link';
import {
  ActivityIcon,
  AlertTriangleIcon,
  BarChart3Icon,
  BuildingIcon,
  CheckCircleIcon,
  ContactIcon,
  CreditCardIcon,
  DatabaseIcon,
  DollarSignIcon,
  ServerIcon,
  ShieldCheckIcon,
  TrendingDownIcon,
  TrendingUpIcon,
  UsersIcon,
  XCircleIcon
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Routes } from '@/constants/routes';
import type { SystemMetricsDto } from '@/types/dtos/system-metrics-dto';

export interface SuperAdminDashboardProps {
  metrics: SystemMetricsDto;
}

export function SuperAdminDashboard({
  metrics
}: SuperAdminDashboardProps): React.JSX.Element {
  const activationRate = (metrics.activeOrganizations / metrics.totalOrganizations) * 100;
  const avgContactsPerOrg = metrics.totalContacts / metrics.totalOrganizations;

  return (
    <div className="space-y-6 p-6">
      {/* System Status Alert */}
      <div className="rounded-lg border border-green-200 bg-green-50 p-4 dark:border-green-800 dark:bg-green-950">
        <div className="flex items-center gap-3">
          <CheckCircleIcon className="size-5 text-green-600 dark:text-green-400" />
          <div>
            <h3 className="font-medium text-green-800 dark:text-green-200">
              System Operational
            </h3>
            <p className="text-sm text-green-700 dark:text-green-300">
              All systems are running normally. Last updated: {new Date().toLocaleTimeString()}
            </p>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Total Organizations"
          value={metrics.totalOrganizations}
          change={metrics.organizationGrowthRate}
          icon={<BuildingIcon className="size-4" />}
          description={`${metrics.organizationsCreatedThisMonth} created this month`}
          trend="up"
          href={Routes.SuperAdminOrganizations}
        />
        <MetricCard
          title="Total Users"
          value={metrics.totalUsers}
          change={metrics.userGrowthRate}
          icon={<UsersIcon className="size-4" />}
          description={`${metrics.usersCreatedThisMonth} created this month`}
          trend="up"
          href={Routes.SuperAdminUsers}
        />
        <MetricCard
          title="Active Organizations"
          value={metrics.activeOrganizations}
          change={activationRate}
          icon={<TrendingUpIcon className="size-4" />}
          description="Completed onboarding"
          trend="neutral"
          isPercentage
        />
        <MetricCard
          title="Total Contacts"
          value={metrics.totalContacts}
          change={avgContactsPerOrg}
          icon={<ContactIcon className="size-4" />}
          description="Avg per organization"
          trend="neutral"
          showAverage
        />
      </div>

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Growth Analytics */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3Icon className="size-5" />
              Growth Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Organization Growth</span>
                    <span className="text-sm text-green-600 dark:text-green-400">
                      +{metrics.organizationGrowthRate}%
                    </span>
                  </div>
                  <Progress value={Math.min(metrics.organizationGrowthRate * 10, 100)} className="h-2" />
                  <p className="text-xs text-muted-foreground">
                    {metrics.organizationsCreatedThisMonth} new organizations this month
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">User Growth</span>
                    <span className="text-sm text-green-600 dark:text-green-400">
                      +{metrics.userGrowthRate}%
                    </span>
                  </div>
                  <Progress value={Math.min(metrics.userGrowthRate * 10, 100)} className="h-2" />
                  <p className="text-xs text-muted-foreground">
                    {metrics.usersCreatedThisMonth} new users this month
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Activation Rate</span>
                  <span className="text-sm font-medium">
                    {activationRate.toFixed(1)}%
                  </span>
                </div>
                <Progress value={activationRate} className="h-2" />
                <p className="text-xs text-muted-foreground">
                  {metrics.activeOrganizations} of {metrics.totalOrganizations} organizations completed onboarding
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* System Health */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ServerIcon className="size-5" />
              System Health
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status</span>
                <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                  <CheckCircleIcon className="mr-1 size-3" />
                  Operational
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Uptime</span>
                <span className="font-medium">99.9%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Response time</span>
                <span className="font-medium">120ms</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Database</span>
                <Badge variant="outline" className="text-green-600">
                  <DatabaseIcon className="mr-1 size-3" />
                  Healthy
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Memory Usage</span>
                <span className="font-medium">68%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions & Recent Activity */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ActivityIcon className="size-5" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3">
              <Button variant="outline" asChild className="h-auto flex-col gap-2 p-4">
                <Link href={Routes.SuperAdminOrganizations}>
                  <BuildingIcon className="size-5" />
                  <span className="text-sm">Manage Organizations</span>
                </Link>
              </Button>
              <Button variant="outline" asChild className="h-auto flex-col gap-2 p-4">
                <Link href={Routes.SuperAdminUsers}>
                  <UsersIcon className="size-5" />
                  <span className="text-sm">Manage Users</span>
                </Link>
              </Button>
              <Button variant="outline" asChild className="h-auto flex-col gap-2 p-4">
                <Link href={Routes.SuperAdminBilling}>
                  <CreditCardIcon className="size-5" />
                  <span className="text-sm">Billing Overview</span>
                </Link>
              </Button>
              <Button variant="outline" asChild className="h-auto flex-col gap-2 p-4">
                <Link href={Routes.SuperAdminSystem}>
                  <ShieldCheckIcon className="size-5" />
                  <span className="text-sm">System Settings</span>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ActivityIcon className="size-5" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="rounded-full bg-green-100 p-1 dark:bg-green-900">
                  <CheckCircleIcon className="size-3 text-green-600 dark:text-green-400" />
                </div>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">New organization created</p>
                  <p className="text-xs text-muted-foreground">Acme Corp joined 2 hours ago</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="rounded-full bg-blue-100 p-1 dark:bg-blue-900">
                  <UsersIcon className="size-3 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">Bulk user registration</p>
                  <p className="text-xs text-muted-foreground">15 new users added to TechStart Inc</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="rounded-full bg-orange-100 p-1 dark:bg-orange-900">
                  <AlertTriangleIcon className="size-3 text-orange-600 dark:text-orange-400" />
                </div>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">Payment failed</p>
                  <p className="text-xs text-muted-foreground">GlobalTech subscription renewal failed</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="rounded-full bg-purple-100 p-1 dark:bg-purple-900">
                  <DollarSignIcon className="size-3 text-purple-600 dark:text-purple-400" />
                </div>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">Subscription upgraded</p>
                  <p className="text-xs text-muted-foreground">StartupXYZ upgraded to Enterprise plan</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* System Alerts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangleIcon className="size-5" />
            System Alerts & Notifications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center gap-3 rounded-lg border border-yellow-200 bg-yellow-50 p-3 dark:border-yellow-800 dark:bg-yellow-950">
              <AlertTriangleIcon className="size-4 text-yellow-600 dark:text-yellow-400" />
              <div className="flex-1">
                <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                  Database backup scheduled
                </p>
                <p className="text-xs text-yellow-700 dark:text-yellow-300">
                  Automated backup will run tonight at 2:00 AM UTC
                </p>
              </div>
              <Badge variant="outline" className="text-yellow-600">
                Scheduled
              </Badge>
            </div>
            <div className="flex items-center gap-3 rounded-lg border border-blue-200 bg-blue-50 p-3 dark:border-blue-800 dark:bg-blue-950">
              <ServerIcon className="size-4 text-blue-600 dark:text-blue-400" />
              <div className="flex-1">
                <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  System maintenance window
                </p>
                <p className="text-xs text-blue-700 dark:text-blue-300">
                  Scheduled maintenance on Sunday 3:00-5:00 AM UTC
                </p>
              </div>
              <Badge variant="outline" className="text-blue-600">
                Upcoming
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

interface MetricCardProps {
  title: string;
  value: number;
  change: number;
  icon: React.ReactNode;
  description: string;
  trend: 'up' | 'down' | 'neutral';
  href?: string;
  isPercentage?: boolean;
  showAverage?: boolean;
}

function MetricCard({
  title,
  value,
  change,
  icon,
  description,
  trend,
  href,
  isPercentage = false,
  showAverage = false
}: MetricCardProps): React.JSX.Element {
  const formatValue = (val: number) => {
    if (isPercentage) return `${val.toFixed(1)}%`;
    if (showAverage) return val.toFixed(1);
    return val.toLocaleString();
  };

  const formatChange = (val: number) => {
    if (isPercentage) return `${val.toFixed(1)}%`;
    if (showAverage) return `${val.toFixed(1)} avg`;
    return `+${val}%`;
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUpIcon className="size-3 text-green-600 dark:text-green-400" />;
      case 'down':
        return <TrendingDownIcon className="size-3 text-red-600 dark:text-red-400" />;
      default:
        return null;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return 'text-green-600 dark:text-green-400';
      case 'down':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-muted-foreground';
    }
  };

  if (href) {
    return (
      <Link href={href}>
        <Card className="cursor-pointer transition-colors hover:bg-muted/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{title}</CardTitle>
            <div className="rounded-lg bg-muted p-2">
              {icon}
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatValue(value)}</div>
            <div className="flex items-center space-x-2">
              {trend !== 'neutral' && (
                <div className={`flex items-center space-x-1 text-xs ${getTrendColor()}`}>
                  {getTrendIcon()}
                  <span>{formatChange(change)}</span>
                </div>
              )}
              <span className="text-xs text-muted-foreground">{description}</span>
            </div>
          </CardContent>
        </Card>
      </Link>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className="rounded-lg bg-muted p-2">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{formatValue(value)}</div>
        <div className="flex items-center space-x-2">
          {trend !== 'neutral' && (
            <div className={`flex items-center space-x-1 text-xs ${getTrendColor()}`}>
              {getTrendIcon()}
              <span>{formatChange(change)}</span>
            </div>
          )}
          <span className="text-xs text-muted-foreground">{description}</span>
        </div>
      </CardContent>
    </Card>
  );
}
