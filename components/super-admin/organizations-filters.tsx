'use client';

import * as React from 'react';
import { SearchIcon } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { DataTableFilter } from '@/components/ui/data-table';
import { InputSearch } from '@/components/ui/input-search';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { MediaQueries } from '@/constants/media-queries';
import { useMediaQuery } from '@/hooks/use-media-query';

export type OrganizationsFiltersProps = {
  searchQuery: string;
  onSearchQueryChange: (value: string) => void;
  statusFilter: string;
  onStatusFilterChange: (value: string) => void;
  tierFilter: string;
  onTierFilterChange: (value: string) => void;
};

const statusOptions = [
  { value: 'all', label: 'All Statuses' },
  { value: 'active', label: 'Active' },
  { value: 'pending', label: 'Pending' },
  { value: 'suspended', label: 'Suspended' }
];

const tierOptions = [
  { value: 'all', label: 'All Tiers' },
  { value: 'free', label: 'Free' },
  { value: 'pro', label: 'Pro' },
  { value: 'enterprise', label: 'Enterprise' }
];

export function OrganizationsFilters({
  searchQuery,
  onSearchQueryChange,
  statusFilter,
  onStatusFilterChange,
  tierFilter,
  onTierFilterChange
}: OrganizationsFiltersProps): React.JSX.Element {
  const [showMobileSearch, setShowMobileSearch] = React.useState<boolean>(false);
  const smUp = useMediaQuery(MediaQueries.SmUp, { fallback: false });

  const handleSearchQueryChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ): void => {
    const value = e.target?.value || '';
    onSearchQueryChange(value);
  };

  const handleShowMobileSearch = (): void => {
    setShowMobileSearch(true);
  };

  return (
    <div className="flex flex-row items-center justify-between gap-4 p-4">
      <div className="flex flex-row items-center gap-2">
        <Select
          value={statusFilter}
          onValueChange={onStatusFilterChange}
        >
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            {statusOptions.map((option) => (
              <SelectItem
                key={option.value}
                value={option.value}
              >
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select
          value={tierFilter}
          onValueChange={onTierFilterChange}
        >
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="Tier" />
          </SelectTrigger>
          <SelectContent>
            {tierOptions.map((option) => (
              <SelectItem
                key={option.value}
                value={option.value}
              >
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div>
        {smUp ? (
          <InputSearch
            placeholder="Search organizations..."
            className="w-[240px]"
            value={searchQuery}
            onChange={handleSearchQueryChange}
          />
        ) : (
          <>
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={handleShowMobileSearch}
            >
              <SearchIcon className="size-4 shrink-0" />
            </Button>
            {showMobileSearch && (
              <div className="absolute inset-0 z-30 bg-background pl-3 pr-5">
                <InputSearch
                  autoFocus
                  alwaysShowClearButton
                  placeholder="Search organizations..."
                  className="w-full"
                  value={searchQuery}
                  onChange={handleSearchQueryChange}
                  onBlur={() => setShowMobileSearch(false)}
                />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
