'use client';

import * as React from 'react';
import { format } from 'date-fns';
import {
  ColumnDef,
  ColumnFiltersState,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  VisibilityState,
  type Row
} from '@tanstack/react-table';
import { MoreHorizontalIcon } from 'lucide-react';

import { UsersFilters } from '@/components/super-admin/users-filters';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DataTable,
  DataTableColumnHeader,
  DataTableColumnOptionsHeader,
  DataTablePagination
} from '@/components/ui/data-table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import { roleLabels } from '@/constants/labels';
import { capitalize, getInitials } from '@/lib/utils';
import type { UserSummaryDto } from '@/types/dtos/user-summary-dto';

export type UsersDataTableProps = {
  data: UserSummaryDto[];
};

export function UsersDataTable({
  data
}: UsersDataTableProps): React.JSX.Element {
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [roleFilter, setRoleFilter] = React.useState('all');
  const [statusFilter, setStatusFilter] = React.useState('all');

  // Helper function to get user status
  const getUserStatus = (user: UserSummaryDto): string => {
    if (!user.completedOnboarding) return 'pending';

    if (user.lastLogin) {
      const daysSinceLogin = Math.floor(
        (Date.now() - new Date(user.lastLogin).getTime()) / (1000 * 60 * 60 * 24)
      );

      if (daysSinceLogin <= 7) return 'active';
      if (daysSinceLogin <= 30) return 'inactive';
    }

    return 'dormant';
  };

  // Filter data based on search and filters
  const filteredData = React.useMemo(() => {
    return data.filter((user) => {
      const matchesSearch = searchQuery === '' ||
        user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.organization?.name.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesRole = roleFilter === 'all' || user.role === roleFilter;
      const matchesStatus = statusFilter === 'all' || getUserStatus(user) === statusFilter;

      return matchesSearch && matchesRole && matchesStatus;
    });
  }, [data, searchQuery, roleFilter, statusFilter]);

  const table = useReactTable({
    data: filteredData,
    columns,
    state: {
      columnVisibility,
      rowSelection,
      columnFilters
    },
    defaultColumn: {
      minSize: 0,
      size: 0
    },
    getRowId: (row) => row.id,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    onRowSelectionChange: setRowSelection,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    enableRowSelection: true
  });

  const handleRowClicked = (row: Row<UserSummaryDto>): void => {
    // TODO: Navigate to user details
    console.log('View user:', row.original.id);
  };

  return (
    <div className="relative flex flex-col overflow-hidden">
      <UsersFilters
        searchQuery={searchQuery}
        onSearchQueryChange={setSearchQuery}
        roleFilter={roleFilter}
        onRoleFilterChange={setRoleFilter}
        statusFilter={statusFilter}
        onStatusFilterChange={setStatusFilter}
      />
      <ScrollArea
        verticalScrollBar
        horizontalScrollBar
        className="h-full"
      >
        <DataTable
          fixedHeader
          table={table}
          wrapperClassName="h-[calc(100svh-241px)] overflow-visible"
          onRowClicked={handleRowClicked}
        />
      </ScrollArea>
      <DataTablePagination table={table} />
    </div>
  );
}

const getRoleBadge = (role: string) => {
  switch (role) {
    case 'SUPER_ADMIN':
      return (
        <Badge variant="destructive">
          {roleLabels[role] || capitalize(role)}
        </Badge>
      );
    case 'ADMIN':
      return (
        <Badge variant="default">
          {roleLabels[role] || capitalize(role)}
        </Badge>
      );
    case 'MEMBER':
      return (
        <Badge variant="secondary">
          {roleLabels[role] || capitalize(role)}
        </Badge>
      );
    default:
      return <Badge variant="outline">{capitalize(role)}</Badge>;
  }
};

const getStatusBadge = (user: UserSummaryDto) => {
  if (!user.completedOnboarding) {
    return <Badge variant="outline">Pending</Badge>;
  }

  if (user.lastLogin) {
    const daysSinceLogin = Math.floor(
      (Date.now() - new Date(user.lastLogin).getTime()) /
        (1000 * 60 * 60 * 24)
    );

    if (daysSinceLogin <= 7) {
      return (
        <Badge
          variant="default"
          className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
        >
          Active
        </Badge>
      );
    } else if (daysSinceLogin <= 30) {
      return <Badge variant="secondary">Inactive</Badge>;
    }
  }

  return <Badge variant="outline">Dormant</Badge>;
};

const columns: ColumnDef<UserSummaryDto>[] = [
  {
    id: 'select',
    size: 64,
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="mx-auto flex items-center justify-center"
        onClick={(e) => e.stopPropagation()}
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="mx-auto flex items-center justify-center"
        onClick={(e) => e.stopPropagation()}
      />
    ),
    enableSorting: false,
    enableHiding: false
  },
  {
    meta: {
      title: 'User'
    },
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="User"
      />
    ),
    cell: ({ row }) => (
      <div className="flex w-fit flex-row items-center gap-3">
        <Avatar className="size-8">
          <AvatarImage src={row.original.image} />
          <AvatarFallback className="text-xs">
            {getInitials(row.original.name)}
          </AvatarFallback>
        </Avatar>
        <div>
          <div className="font-medium">{row.original.name}</div>
          <div className="text-sm text-muted-foreground">
            {row.original.email}
          </div>
        </div>
      </div>
    ),
    enableSorting: true,
    enableHiding: true
  },
  {
    meta: {
      title: 'Role'
    },
    accessorKey: 'role',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Role"
      />
    ),
    cell: ({ row }) => getRoleBadge(row.original.role),
    enableSorting: true,
    enableHiding: true
  },
  {
    meta: {
      title: 'Status'
    },
    accessorKey: 'completedOnboarding',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Status"
      />
    ),
    cell: ({ row }) => getStatusBadge(row.original),
    enableSorting: true,
    enableHiding: true
  },
  {
    meta: {
      title: 'Organization'
    },
    accessorKey: 'organization.name',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Organization"
      />
    ),
    cell: ({ row }) => (
      <div>
        {row.original.organization ? (
          <>
            <div className="font-medium">
              {row.original.organization.name}
            </div>
            <div className="text-xs text-muted-foreground">
              {capitalize(row.original.organization.tier)}
            </div>
          </>
        ) : (
          <span className="text-muted-foreground">
            No organization
          </span>
        )}
      </div>
    ),
    enableSorting: true,
    enableHiding: true
  },
  {
    meta: {
      title: 'Last Login'
    },
    accessorKey: 'lastLogin',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Last Login"
      />
    ),
    cell: ({ row }) => (
      <span className="whitespace-nowrap text-sm">
        {row.original.lastLogin ? (
          format(new Date(row.original.lastLogin), 'MMM dd, yyyy')
        ) : (
          <span className="text-muted-foreground">Never</span>
        )}
      </span>
    ),
    enableSorting: true,
    enableHiding: true
  },
  {
    meta: {
      title: 'Created'
    },
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Created"
      />
    ),
    cell: ({ row }) => (
      <span className="whitespace-nowrap text-sm">
        {format(new Date(row.original.createdAt), 'MMM dd, yyyy')}
      </span>
    ),
    enableSorting: true,
    enableHiding: true
  },
  {
    id: 'actions',
    size: 64,
    header: ({ table }) => <DataTableColumnOptionsHeader table={table} />,
    cell: ({ row }) => {
      const handleViewDetails = (): void => {
        console.log('View details for user:', row.original.id);
      };

      const handleImpersonate = (): void => {
        console.log('Impersonate user:', row.original.id);
      };

      const handleSuspendUser = (): void => {
        console.log('Suspend user:', row.original.id);
      };

      const handleDeleteUser = (): void => {
        console.log('Delete user:', row.original.id);
      };

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="size-8 p-0"
              title="Open menu"
              onClick={(e) => e.stopPropagation()}
            >
              <MoreHorizontalIcon className="size-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleViewDetails}>
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={handleImpersonate}
              className="text-blue-600"
            >
              Impersonate
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={handleSuspendUser}
              className="text-orange-600"
            >
              Suspend
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={handleDeleteUser}
              className="text-destructive"
            >
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
    enableSorting: false,
    enableHiding: false
  }
];
