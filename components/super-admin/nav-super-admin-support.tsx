'use client';

import * as React from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeftIcon } from 'lucide-react';

import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  type SidebarGroupProps
} from '@/components/ui/sidebar';
import { Routes } from '@/constants/routes';
import type { ProfileDto } from '@/types/dtos/profile-dto';

export type NavSuperAdminSupportProps = SidebarGroupProps & {
  profile: ProfileDto;
};

export function NavSuperAdminSupport({
  profile,
  ...other
}: NavSuperAdminSupportProps): React.JSX.Element {
  const router = useRouter();

  const handleBackToDashboard = (): void => {
    router.push(Routes.Dashboard);
  };

  return (
    <SidebarGroup {...other}>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton
            type="button"
            tooltip="Back to Dashboard"
            className="text-muted-foreground"
            onClick={handleBackToDashboard}
          >
            <ArrowLeftIcon className="size-4 shrink-0" />
            <span>Back to Dashboard</span>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarGroup>
  );
}
