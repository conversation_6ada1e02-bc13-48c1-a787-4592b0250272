'use client';

import * as React from 'react';
import { format } from 'date-fns';
import {
  ColumnDef,
  ColumnFiltersState,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  VisibilityState,
  type Row
} from '@tanstack/react-table';
import { MoreHorizontalIcon } from 'lucide-react';

import { OrganizationsFilters } from '@/components/super-admin/organizations-filters';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DataTable,
  DataTableColumnHeader,
  DataTableColumnOptionsHeader,
  DataTablePagination
} from '@/components/ui/data-table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import { capitalize } from '@/lib/utils';
import type { OrganizationSummaryDto } from '@/types/dtos/organization-summary-dto';

export type OrganizationsDataTableProps = {
  data: OrganizationSummaryDto[];
};

export function OrganizationsDataTable({
  data
}: OrganizationsDataTableProps): React.JSX.Element {
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [statusFilter, setStatusFilter] = React.useState('all');
  const [tierFilter, setTierFilter] = React.useState('all');

  // Filter data based on search and filters
  const filteredData = React.useMemo(() => {
    return data.filter((org) => {
      const matchesSearch = searchQuery === '' ||
        org.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        org.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        org.primaryAdmin?.name.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesStatus = statusFilter === 'all' || org.status === statusFilter;
      const matchesTier = tierFilter === 'all' || org.tier.toLowerCase() === tierFilter;

      return matchesSearch && matchesStatus && matchesTier;
    });
  }, [data, searchQuery, statusFilter, tierFilter]);

  const table = useReactTable({
    data: filteredData,
    columns,
    state: {
      columnVisibility,
      rowSelection,
      columnFilters
    },
    defaultColumn: {
      minSize: 0,
      size: 0
    },
    getRowId: (row) => row.id,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    onRowSelectionChange: setRowSelection,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    enableRowSelection: true
  });

  const handleRowClicked = (row: Row<OrganizationSummaryDto>): void => {
    // TODO: Navigate to organization details
    console.log('View organization:', row.original.id);
  };

  return (
    <div className="relative flex flex-col overflow-hidden">
      <OrganizationsFilters
        searchQuery={searchQuery}
        onSearchQueryChange={setSearchQuery}
        statusFilter={statusFilter}
        onStatusFilterChange={setStatusFilter}
        tierFilter={tierFilter}
        onTierFilterChange={setTierFilter}
      />
      <ScrollArea
        verticalScrollBar
        horizontalScrollBar
        className="h-full"
      >
        <DataTable
          fixedHeader
          table={table}
          wrapperClassName="h-[calc(100svh-241px)] overflow-visible"
          onRowClicked={handleRowClicked}
        />
      </ScrollArea>
      <DataTablePagination table={table} />
    </div>
  );
}

const getStatusBadge = (status: string) => {
  switch (status) {
    case 'active':
      return (
        <Badge
          variant="default"
          className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
        >
          Active
        </Badge>
      );
    case 'pending':
      return <Badge variant="secondary">Pending</Badge>;
    case 'suspended':
      return <Badge variant="destructive">Suspended</Badge>;
    default:
      return <Badge variant="outline">{capitalize(status)}</Badge>;
  }
};

const getTierBadge = (tier: string) => {
  switch (tier.toLowerCase()) {
    case 'free':
      return <Badge variant="outline">Free</Badge>;
    case 'pro':
      return <Badge variant="default">Pro</Badge>;
    case 'enterprise':
      return (
        <Badge
          variant="default"
          className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300"
        >
          Enterprise
        </Badge>
      );
    default:
      return <Badge variant="outline">{capitalize(tier)}</Badge>;
  }
};

const columns: ColumnDef<OrganizationSummaryDto>[] = [
  {
    id: 'select',
    size: 64,
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="mx-auto flex items-center justify-center"
        onClick={(e) => e.stopPropagation()}
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="mx-auto flex items-center justify-center"
        onClick={(e) => e.stopPropagation()}
      />
    ),
    enableSorting: false,
    enableHiding: false
  },
  {
    meta: {
      title: 'Organization'
    },
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Organization"
      />
    ),
    cell: ({ row }) => (
      <div className="flex w-fit flex-col">
        <div className="font-medium">{row.original.name}</div>
        {row.original.email && (
          <div className="text-sm text-muted-foreground">
            {row.original.email}
          </div>
        )}
        {row.original.primaryAdmin && (
          <div className="text-xs text-muted-foreground">
            Admin: {row.original.primaryAdmin.name}
          </div>
        )}
      </div>
    ),
    enableSorting: true,
    enableHiding: true
  },
  {
    meta: {
      title: 'Status'
    },
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Status"
      />
    ),
    cell: ({ row }) => getStatusBadge(row.original.status),
    enableSorting: true,
    enableHiding: true
  },
  {
    meta: {
      title: 'Tier'
    },
    accessorKey: 'tier',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Tier"
      />
    ),
    cell: ({ row }) => getTierBadge(row.original.tier),
    enableSorting: true,
    enableHiding: true
  },
  {
    meta: {
      title: 'Users'
    },
    accessorKey: 'userCount',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Users"
      />
    ),
    cell: ({ row }) => (
      <span className="font-medium">{row.original.userCount}</span>
    ),
    enableSorting: true,
    enableHiding: true
  },
  {
    meta: {
      title: 'Contacts'
    },
    accessorKey: 'contactCount',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Contacts"
      />
    ),
    cell: ({ row }) => (
      <span className="font-medium">{row.original.contactCount}</span>
    ),
    enableSorting: true,
    enableHiding: true
  },
  {
    meta: {
      title: 'Created'
    },
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Created"
      />
    ),
    cell: ({ row }) => (
      <span className="whitespace-nowrap text-sm">
        {format(new Date(row.original.createdAt), 'MMM dd, yyyy')}
      </span>
    ),
    enableSorting: true,
    enableHiding: true
  },
  {
    id: 'actions',
    size: 64,
    header: ({ table }) => <DataTableColumnOptionsHeader table={table} />,
    cell: ({ row }) => {
      const handleViewDetails = (): void => {
        console.log('View details for organization:', row.original.id);
      };

      const handleSuspendOrganization = (): void => {
        console.log('Suspend organization:', row.original.id);
      };

      const handleDeleteOrganization = (): void => {
        console.log('Delete organization:', row.original.id);
      };

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="size-8 p-0"
              title="Open menu"
              onClick={(e) => e.stopPropagation()}
            >
              <MoreHorizontalIcon className="size-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleViewDetails}>
              View Details
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={handleSuspendOrganization}
              className="text-orange-600"
            >
              Suspend
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={handleDeleteOrganization}
              className="text-destructive"
            >
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
    enableSorting: false,
    enableHiding: false
  }
];
