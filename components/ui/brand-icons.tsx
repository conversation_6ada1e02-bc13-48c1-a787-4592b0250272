import * as React from 'react';

export function LinkedInIcon(
  props: React.SVGAttributes<SVGSVGElement>
): React.JSX.Element {
  return (
    <svg
      width="16"
      height="16"
      strokeLinejoin="round"
      color="currentcolor"
      viewBox="0 0 16 16"
      {...props}
    >
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M2 0a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V2a2 2 0 00-2-2H2zm3 6.75V13H3V6.75h2zM5 4.5c0 .556-.386 1-1.006 1h-.012C3.386 5.5 3 5.056 3 4.5c0-.568.398-1 1.006-1s.982.432.994 1zM8.5 13h-2s.032-5.568 0-6.25h2v1.034s.5-1.034 2-1.034 2.5.848 2.5 3.081V13h-2v-2.89s0-1.644-1.264-1.644S8.5 9.94 8.5 9.94V13z"
        clipRule="evenodd"
      />
    </svg>
  );
}

export function XIcon(
  props: React.SVGAttributes<SVGSVGElement>
): React.JSX.Element {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      strokeLinejoin="round"
      color="currentcolor"
      viewBox="0 0 16 16"
      {...props}
    >
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M.5.5h5.25l3.734 5.21L14 .5h2l-5.61 6.474L16.5 15.5h-5.25l-3.734-5.21L3 15.5H1l5.61-6.474L.5.5zM12.02 14L3.42 2h1.56l8.6 12h-1.56z"
        clipRule="evenodd"
      />
    </svg>
  );
}

export function YouTubeIcon(
  props: React.SVGAttributes<SVGSVGElement>
): React.JSX.Element {
  return (
    <svg
      width="16"
      height="16"
      strokeLinejoin="round"
      color="currentcolor"
      viewBox="0 0 16 16"
      {...props}
    >
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M14.251 3.435a2.003 2.003 0 011.414 1.414C16 6.097 16 8.7 16 8.7s0 2.605-.335 3.851a2.003 2.003 0 01-1.415 1.415C13.006 14.3 8 14.3 8 14.3s-5.003 0-6.251-.334a2.004 2.004 0 01-1.414-1.415C0 11.305 0 8.7 0 8.7s0-2.603.335-3.851a2.003 2.003 0 011.414-1.414C2.997 3.1 8 3.1 8 3.1s5.003 0 6.251.335zM10.555 8.7L6.4 11.1V6.3l4.155 2.4z"
        clipRule="evenodd"
      />
    </svg>
  );
}

export function InstagramIcon(
  props: React.SVGAttributes<SVGSVGElement>
): React.JSX.Element {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="currentColor"
      viewBox="0 0 16 16"
      {...props}
    >
      <path
        fill="currentColor"
        d="M10.088 7.688a2.4 2.4 0 11-4.8 0 2.4 2.4 0 014.8 0zm5.1-3.3v6.6a4.205 4.205 0 01-4.2 4.2h-6.6a4.205 4.205 0 01-4.2-4.2v-6.6a4.205 4.205 0 014.2-4.2h6.6a4.205 4.205 0 014.2 4.2zm-3.9 3.3a3.6 3.6 0 10-7.2 0 3.6 3.6 0 007.2 0zm1.2-3.9a.9.9 0 10-1.8 0 .9.9 0 001.8 0z"
      />
    </svg>
  );
}

export function TikTokIcon(
  props: React.SVGAttributes<SVGSVGElement>
): React.JSX.Element {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      width="24"
      height="24"
      fill="currentColor"
      {...props}
    >
      <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z" />
    </svg>
  );
}

export function FacebookIcon(
  props: React.SVGAttributes<SVGSVGElement>
): React.JSX.Element {
  return (
    <svg
      width="16"
      height="16"
      strokeLinejoin="round"
      color="currentcolor"
      viewBox="0 0 16 16"
      {...props}
    >
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M9.003 15.938A8.001 8.001 0 008 0a8 8 0 00-1.75 15.808V10.43H4.5V8h1.75V6.94c0-2.718 1.035-3.976 3.701-3.976.505 0 1.377.099 1.734.198v2.21c-.188-.02-.517-.03-.923-.03C9.454 5.343 9 5.839 9 7.129V8h2.558l-.447 2.43H9.003v5.508z"
        clipRule="evenodd"
      />
    </svg>
  );
}
