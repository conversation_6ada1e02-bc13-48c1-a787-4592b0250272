'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { CreditCardIcon, UnlockIcon, AlertTriangleIcon } from 'lucide-react';

interface WorkspaceProfile {
  id: string;
  title: string | null;
  description: string | null;
  aiScore: number | null;
  profile: {
    id: string;
    linkedinUrl: string;
    profileData: any;
    scrapedAt: Date | null;
  };
}

interface UnlockProfilesModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedProfiles: WorkspaceProfile[];
  onConfirm: () => void;
  isLoading?: boolean;
}

export function UnlockProfilesModal({
  isOpen,
  onClose,
  selectedProfiles,
  onConfirm,
  isLoading = false
}: UnlockProfilesModalProps) {
  const creditsRequired = selectedProfiles.length;
  const maxProfiles = 10;

  const getProfileInitials = (name: string): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getScoreBadge = (score: number | null) => {
    if (score === null) {
      return (
        <Badge variant="secondary" className="gap-1 text-gray-500">
          <div className="size-2 rounded-full bg-gray-400" />
          0.0
        </Badge>
      );
    }

    if (score >= 8.5) {
      return (
        <Badge variant="default" className="gap-1 bg-green-600 hover:bg-green-700">
          <div className="size-2 rounded-full bg-green-200" />
          {score.toFixed(1)}
        </Badge>
      );
    }

    if (score >= 6) {
      return (
        <Badge variant="secondary" className="gap-1 bg-yellow-500 hover:bg-yellow-600 text-white">
          <div className="size-2 rounded-full bg-yellow-200" />
          {score.toFixed(1)}
        </Badge>
      );
    }

    return (
      <Badge variant="destructive" className="gap-1">
        <div className="size-2 rounded-full bg-red-200" />
        {score.toFixed(1)}
      </Badge>
    );
  };

  const canProceed = selectedProfiles.length > 0 && selectedProfiles.length <= maxProfiles;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UnlockIcon className="size-5" />
            Unlock LinkedIn Profiles
          </DialogTitle>
          <DialogDescription>
            Scrape detailed profile data including contact information, work experience, and company details.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-4">
          {/* Credit Usage Info */}
          <div className="rounded-lg border bg-blue-50 border-blue-200 p-4">
            <div className="flex items-center gap-3">
              <CreditCardIcon className="size-5 text-blue-600" />
              <div className="flex-1">
                <div className="font-medium text-blue-900">Credit Usage</div>
                <div className="text-sm text-blue-700">
                  {creditsRequired} credit{creditsRequired !== 1 ? 's' : ''} will be used for this operation
                </div>
              </div>
              <div className="text-lg font-bold text-blue-900">
                {creditsRequired} {creditsRequired === 1 ? 'Credit' : 'Credits'}
              </div>
            </div>
          </div>

          {/* Warning for max profiles */}
          {selectedProfiles.length > maxProfiles && (
            <div className="rounded-lg border bg-orange-50 border-orange-200 p-4">
              <div className="flex items-center gap-3">
                <AlertTriangleIcon className="size-5 text-orange-600" />
                <div className="flex-1">
                  <div className="font-medium text-orange-900">Too Many Profiles Selected</div>
                  <div className="text-sm text-orange-700">
                    Maximum {maxProfiles} profiles can be unlocked at once. Please reduce your selection.
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Selected Profiles List */}
          <div className="space-y-3">
            <div className="font-medium text-sm text-muted-foreground">
              Selected Profiles ({selectedProfiles.length})
            </div>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {selectedProfiles.map((profile) => {
                const name = profile.title?.split(' - ')[0] || 'Unknown';
                const position = profile.title?.split(' - ')[1] || '';
                
                return (
                  <div key={profile.id} className="flex items-center gap-3 p-3 rounded-lg border bg-card">
                    <Avatar className="size-10">
                      <AvatarImage src="" alt={name} />
                      <AvatarFallback className="text-xs">
                        {getProfileInitials(name)}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm truncate">{name}</div>
                      {position && (
                        <div className="text-xs text-muted-foreground truncate">{position}</div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {getScoreBadge(profile.aiScore)}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* What will be scraped */}
          <div className="rounded-lg border p-4">
            <div className="font-medium text-sm mb-2">What will be scraped:</div>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Full profile information (name, headline, about)</li>
              <li>• Work experience and current company details</li>
              <li>• Education background and skills</li>
              <li>• Contact information (if available)</li>
              <li>• Company website emails (if company website exists)</li>
            </ul>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button 
            onClick={onConfirm} 
            disabled={!canProceed || isLoading}
            className="gap-2"
          >
            {isLoading ? (
              <>
                <div className="size-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                Unlocking...
              </>
            ) : (
              <>
                <UnlockIcon className="size-4" />
                Unlock {creditsRequired} Profile{creditsRequired !== 1 ? 's' : ''}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
