import * as React from 'react';
import { UserIcon, BuildingIcon, SearchIcon, ClockIcon } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatDistanceToNow } from 'date-fns';

interface WorkspaceStatsProps {
  workspace: {
    id: string;
    name: string;
    description: string | null;
    targetType: 'PERSON' | 'COMPANY';
    industry: string | null;
    country: string | null;
    language: string | null;
    createdAt: Date;
    updatedAt: Date;
    _count: {
      profiles: number;
      searches: number;
    };
  };
}

export function WorkspaceStats({ workspace }: WorkspaceStatsProps) {
  const stats = [
    {
      title: 'Target Type',
      value: workspace.targetType === 'PERSON' ? 'People' : 'Companies',
      icon: workspace.targetType === 'PERSON' ? UserIcon : BuildingIcon,
      color: workspace.targetType === 'PERSON' ? 'text-blue-600' : 'text-green-600'
    },
    {
      title: 'Profiles Found',
      value: workspace._count.profiles.toLocaleString(),
      icon: UserIcon,
      color: 'text-purple-600'
    },
    {
      title: 'Searches Run',
      value: workspace._count.searches.toLocaleString(),
      icon: SearchIcon,
      color: 'text-orange-600'
    },
    {
      title: 'Last Updated',
      value: formatDistanceToNow(workspace.updatedAt, { addSuffix: true }),
      icon: ClockIcon,
      color: 'text-gray-600'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <div className="flex items-center gap-3">
            {workspace.targetType === 'PERSON' ? (
              <UserIcon className="size-6 text-blue-600" />
            ) : (
              <BuildingIcon className="size-6 text-green-600" />
            )}
            <h1 className="text-2xl font-bold">{workspace.name}</h1>
          </div>
          {workspace.description && (
            <p className="text-muted-foreground max-w-2xl">{workspace.description}</p>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={workspace.targetType === 'PERSON' ? 'default' : 'secondary'}>
            {workspace.targetType === 'PERSON' ? 'People' : 'Companies'}
          </Badge>
          {workspace.industry && (
            <Badge variant="outline">{workspace.industry}</Badge>
          )}
          {workspace.country && (
            <Badge variant="outline">{workspace.country.toUpperCase()}</Badge>
          )}
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {stat.title}
                </CardTitle>
                <Icon className={`size-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Additional Info */}
      <div className="text-xs text-muted-foreground">
        Created {formatDistanceToNow(workspace.createdAt, { addSuffix: true })}
      </div>
    </div>
  );
}
