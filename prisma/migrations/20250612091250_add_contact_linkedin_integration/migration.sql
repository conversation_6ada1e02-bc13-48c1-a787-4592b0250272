-- AlterTable
ALTER TABLE "Contact" ADD COLUMN     "linkedinProfileId" UUID,
ADD COLUMN     "linkedinUrl" VARCHAR(500);

-- CreateTable
CREATE TABLE "ContactEmail" (
    "id" UUID NOT NULL,
    "contactId" UUID NOT NULL,
    "email" VARCHAR(255) NOT NULL,
    "type" VARCHAR(50),
    "isPrimary" BOOLEAN NOT NULL DEFAULT false,
    "source" VARCHAR(100),
    "sources" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PK_ContactEmail" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ContactPhone" (
    "id" UUID NOT NULL,
    "contactId" UUID NOT NULL,
    "phone" VARCHAR(32) NOT NULL,
    "type" VARCHAR(50),
    "isPrimary" BOOLEAN NOT NULL DEFAULT false,
    "source" VARCHAR(100),
    "sources" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PK_ContactPhone" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "IX_ContactEmail_contactId" ON "ContactEmail"("contactId");

-- CreateIndex
CREATE INDEX "IX_ContactEmail_email" ON "ContactEmail"("email");

-- CreateIndex
CREATE UNIQUE INDEX "ContactEmail_contactId_email_key" ON "ContactEmail"("contactId", "email");

-- CreateIndex
CREATE INDEX "IX_ContactPhone_contactId" ON "ContactPhone"("contactId");

-- CreateIndex
CREATE INDEX "IX_ContactPhone_phone" ON "ContactPhone"("phone");

-- CreateIndex
CREATE UNIQUE INDEX "ContactPhone_contactId_phone_key" ON "ContactPhone"("contactId", "phone");

-- CreateIndex
CREATE INDEX "IX_Contact_linkedinProfileId" ON "Contact"("linkedinProfileId");

-- CreateIndex
CREATE INDEX "IX_Contact_linkedinUrl" ON "Contact"("linkedinUrl");

-- AddForeignKey
ALTER TABLE "Contact" ADD CONSTRAINT "Contact_linkedinProfileId_fkey" FOREIGN KEY ("linkedinProfileId") REFERENCES "LinkedInProfile"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContactEmail" ADD CONSTRAINT "ContactEmail_contactId_fkey" FOREIGN KEY ("contactId") REFERENCES "Contact"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContactPhone" ADD CONSTRAINT "ContactPhone_contactId_fkey" FOREIGN KEY ("contactId") REFERENCES "Contact"("id") ON DELETE CASCADE ON UPDATE CASCADE;
