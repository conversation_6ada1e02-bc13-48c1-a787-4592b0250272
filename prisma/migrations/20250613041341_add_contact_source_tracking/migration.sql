-- AlterTable
ALTER TABLE "Contact" ADD COLUMN     "dataQuality" INTEGER DEFAULT 0,
ADD COLUMN     "lastEnriched" TIMESTAMP(3),
ADD COLUMN     "source" VARCHAR(50),
ADD COLUMN     "sourceUrl" VARCHAR(500);

-- CreateTable
CREATE TABLE "Company" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "linkedinId" VARCHAR(100),
    "name" VARCHAR(255) NOT NULL,
    "website" VARCHAR(500),
    "domain" VARCHAR(255),
    "description" TEXT,
    "industry" VARCHAR(255),
    "employeeCount" VARCHAR(100),
    "foundedYear" INTEGER,
    "headquarters" JSONB,
    "logo" VARCHAR(500),
    "companyData" JSONB,
    "scrapedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_Company" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ContactCompany" (
    "id" UUID NOT NULL,
    "contactId" UUID NOT NULL,
    "companyId" UUID NOT NULL,
    "role" VARCHAR(255),
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "isCurrent" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PK_ContactCompany" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Company_linkedinId_key" ON "Company"("linkedinId");

-- CreateIndex
CREATE INDEX "IX_Company_organizationId" ON "Company"("organizationId");

-- CreateIndex
CREATE INDEX "IX_Company_linkedinId" ON "Company"("linkedinId");

-- CreateIndex
CREATE INDEX "IX_Company_domain" ON "Company"("domain");

-- CreateIndex
CREATE INDEX "IX_ContactCompany_contactId" ON "ContactCompany"("contactId");

-- CreateIndex
CREATE INDEX "IX_ContactCompany_companyId" ON "ContactCompany"("companyId");

-- CreateIndex
CREATE UNIQUE INDEX "ContactCompany_contactId_companyId_key" ON "ContactCompany"("contactId", "companyId");

-- AddForeignKey
ALTER TABLE "Company" ADD CONSTRAINT "Company_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContactCompany" ADD CONSTRAINT "ContactCompany_contactId_fkey" FOREIGN KEY ("contactId") REFERENCES "Contact"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContactCompany" ADD CONSTRAINT "ContactCompany_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE CASCADE ON UPDATE CASCADE;
