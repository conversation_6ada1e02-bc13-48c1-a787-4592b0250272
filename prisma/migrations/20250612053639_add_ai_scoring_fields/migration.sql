-- CreateEnum
CREATE TYPE "WorkspaceTarget" AS ENUM ('person', 'company');

-- Create<PERSON>num
CREATE TYPE "WorkspaceSearchStatus" AS ENUM ('pending', 'running', 'completed', 'failed');

-- CreateTable
CREATE TABLE "Workspace" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "description" VARCHAR(1000),
    "targetType" "WorkspaceTarget" NOT NULL DEFAULT 'person',
    "industry" VARCHAR(100),
    "country" VARCHAR(10),
    "language" VARCHAR(10),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_Workspace" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WorkspaceSearch" (
    "id" UUID NOT NULL,
    "workspaceId" UUID NOT NULL,
    "query" VARCHAR(500) NOT NULL,
    "searchDork" VARCHAR(1000) NOT NULL,
    "status" "WorkspaceSearchStatus" NOT NULL DEFAULT 'pending',
    "resultsCount" INTEGER,
    "executedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PK_WorkspaceSearch" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LinkedInProfile" (
    "id" UUID NOT NULL,
    "linkedinUrl" VARCHAR(500) NOT NULL,
    "profileData" JSONB,
    "scrapedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_LinkedInProfile" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WorkspaceProfile" (
    "id" UUID NOT NULL,
    "workspaceId" UUID NOT NULL,
    "profileId" UUID NOT NULL,
    "foundInSearch" VARCHAR(500) NOT NULL,
    "title" VARCHAR(255),
    "description" VARCHAR(1000),
    "addedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "aiScore" DOUBLE PRECISION,
    "aiReason" VARCHAR(500),
    "aiExtractedData" JSONB,
    "aiProcessedAt" TIMESTAMP(3),

    CONSTRAINT "PK_WorkspaceProfile" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "IX_Workspace_organizationId" ON "Workspace"("organizationId");

-- CreateIndex
CREATE INDEX "IX_WorkspaceSearch_workspaceId" ON "WorkspaceSearch"("workspaceId");

-- CreateIndex
CREATE UNIQUE INDEX "LinkedInProfile_linkedinUrl_key" ON "LinkedInProfile"("linkedinUrl");

-- CreateIndex
CREATE INDEX "IX_LinkedInProfile_linkedinUrl" ON "LinkedInProfile"("linkedinUrl");

-- CreateIndex
CREATE INDEX "IX_WorkspaceProfile_workspaceId" ON "WorkspaceProfile"("workspaceId");

-- CreateIndex
CREATE INDEX "IX_WorkspaceProfile_profileId" ON "WorkspaceProfile"("profileId");

-- CreateIndex
CREATE INDEX "IX_WorkspaceProfile_aiScore" ON "WorkspaceProfile"("aiScore");

-- CreateIndex
CREATE UNIQUE INDEX "WorkspaceProfile_workspaceId_profileId_key" ON "WorkspaceProfile"("workspaceId", "profileId");

-- AddForeignKey
ALTER TABLE "Workspace" ADD CONSTRAINT "Workspace_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkspaceSearch" ADD CONSTRAINT "WorkspaceSearch_workspaceId_fkey" FOREIGN KEY ("workspaceId") REFERENCES "Workspace"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkspaceProfile" ADD CONSTRAINT "WorkspaceProfile_workspaceId_fkey" FOREIGN KEY ("workspaceId") REFERENCES "Workspace"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkspaceProfile" ADD CONSTRAINT "WorkspaceProfile_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "LinkedInProfile"("id") ON DELETE CASCADE ON UPDATE CASCADE;
