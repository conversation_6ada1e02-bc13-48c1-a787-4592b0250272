import { z } from 'zod';

export const searchWorkspaceSchema = z.object({
  workspaceId: z.string().uuid('Invalid workspace ID'),
  query: z
    .string()
    .min(1, 'Search query is required')
    .max(500, 'Search query must be less than 500 characters'),
  locations: z
    .array(z.string())
    .optional()
    .default([])
});

export type SearchWorkspaceSchema = z.infer<typeof searchWorkspaceSchema>;
