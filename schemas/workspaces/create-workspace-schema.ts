import { z } from 'zod';

export const createWorkspaceSchema = z.object({
  name: z
    .string()
    .min(1, 'Workspace name is required')
    .max(255, 'Workspace name must be less than 255 characters'),
  description: z
    .string()
    .max(1000, 'Description must be less than 1000 characters')
    .optional(),
  targetType: z.enum(['PERSON', 'COMPANY'], {
    required_error: 'Target type is required'
  }),
  industry: z
    .string()
    .max(100, 'Industry must be less than 100 characters')
    .optional(),
  country: z
    .string()
    .max(10, 'Country code must be less than 10 characters')
    .optional(),
  language: z
    .string()
    .max(10, 'Language code must be less than 10 characters')
    .optional()
});

export type CreateWorkspaceSchema = z.infer<typeof createWorkspaceSchema>;
